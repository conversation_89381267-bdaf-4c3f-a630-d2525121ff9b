<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.personal.blog.mapper.CategoryMapper">

    <!-- 分类VO结果映射 -->
    <resultMap id="CategoryVOMap" type="com.personal.blog.vo.CategoryVO">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="description" property="description"/>
        <result column="parent_id" property="parentId"/>
        <result column="sort_order" property="sortOrder"/>
        <result column="article_count" property="articleCount"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 获取所有分类及其文章数量 -->
    <select id="selectCategoriesWithArticleCount" resultMap="CategoryVOMap">
        SELECT 
            c.id,
            c.name,
            c.description,
            c.parent_id,
            c.sort_order,
            c.create_time,
            COALESCE(COUNT(a.id), 0) as article_count
        FROM tb_category c
        LEFT JOIN tb_article a ON c.id = a.category_id AND a.is_published = 1
        GROUP BY c.id, c.name, c.description, c.parent_id, c.sort_order, c.create_time
        ORDER BY c.sort_order ASC, c.id ASC
    </select>

    <!-- 获取热门分类（文章数量最多的前N个） -->
    <select id="selectHotCategories" resultMap="CategoryVOMap">
        SELECT 
            c.id,
            c.name,
            c.description,
            c.parent_id,
            c.sort_order,
            c.create_time,
            COALESCE(COUNT(a.id), 0) as article_count
        FROM tb_category c
        LEFT JOIN tb_article a ON c.id = a.category_id AND a.is_published = 1
        GROUP BY c.id, c.name, c.description, c.parent_id, c.sort_order, c.create_time
        HAVING article_count > 0
        ORDER BY article_count DESC, c.sort_order ASC
        LIMIT #{limit}
    </select>

</mapper> 