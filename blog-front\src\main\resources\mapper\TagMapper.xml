<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.personal.blog.mapper.TagMapper">

    <!-- 标签VO结果映射 -->
    <resultMap id="TagVOMap" type="com.personal.blog.vo.TagVO">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="description" property="description"/>
        <result column="color" property="color"/>
        <result column="sort_order" property="sortOrder"/>
        <result column="article_count" property="articleCount"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 获取所有标签及其文章数量 -->
    <select id="selectTagsWithArticleCount" resultMap="TagVOMap">
        SELECT 
            t.id,
            t.name,
            t.description,
            t.color,
            t.sort_order,
            t.create_time,
            COALESCE(COUNT(at.article_id), 0) as article_count
        FROM tb_tag t
        LEFT JOIN tb_article_tag at ON t.id = at.tag_id
        LEFT JOIN tb_article a ON at.article_id = a.id AND a.is_published = 1
        GROUP BY t.id, t.name, t.description, t.color, t.sort_order, t.create_time
        ORDER BY t.sort_order ASC, t.id ASC
    </select>

    <!-- 获取热门标签（文章数量最多的前N个） -->
    <select id="selectHotTags" resultMap="TagVOMap">
        SELECT 
            t.id,
            t.name,
            t.description,
            t.color,
            t.sort_order,
            t.create_time,
            COALESCE(COUNT(at.article_id), 0) as article_count
        FROM tb_tag t
        LEFT JOIN tb_article_tag at ON t.id = at.tag_id
        LEFT JOIN tb_article a ON at.article_id = a.id AND a.is_published = 1
        GROUP BY t.id, t.name, t.description, t.color, t.sort_order, t.create_time
        HAVING article_count > 0
        ORDER BY article_count DESC, t.sort_order ASC
        LIMIT #{limit}
    </select>

</mapper> 