import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { 
  getArticles, 
  getArticleById, 
  getCategories, 
  searchArticles,
  getHotArticles 
} from '@/api/article'

export const useArticleStore = defineStore('article', () => {
  // 状态
  const articles = ref([])
  const currentArticle = ref({})
  const categories = ref([])
  const hotArticles = ref([])
  const searchResults = ref([])
  const loading = ref(false)
  const pagination = ref({
    current: 1,
    size: 12,
    total: 0
  })

  // 计算属性
  const hasMore = computed(() => {
    return pagination.value.current * pagination.value.size < pagination.value.total
  })

  // 获取文章列表
  const fetchArticles = async (params = {}) => {
    try {
      loading.value = true

      // 清理参数，确保类型正确
      const cleanParams = {
        current: pagination.value.current,
        size: pagination.value.size
      }

      // 只添加有效的参数
      if (params.categoryId && typeof params.categoryId === 'number') {
        cleanParams.categoryId = params.categoryId
      }
      if (params.sortBy && typeof params.sortBy === 'string') {
        cleanParams.sortBy = params.sortBy
      }
      if (params.sortOrder && typeof params.sortOrder === 'string') {
        cleanParams.sortOrder = params.sortOrder
      }

      const response = await getArticles(cleanParams)

      const { records, current, size, total } = response.data

      if (current === 1) {
        articles.value = records
      } else {
        articles.value.push(...records)
      }

      pagination.value = { current, size, total }

      return { success: true }
    } catch (error) {
      console.error('获取文章列表失败:', error)
      return { success: false, message: '获取文章列表失败' }
    } finally {
      loading.value = false
    }
  }

  // 获取热门文章
  const fetchHotArticles = async (params = {}) => {
    try {
      loading.value = true

      const cleanParams = {
        current: pagination.value.current,
        size: pagination.value.size,
        ...params
      }

      const response = await getHotArticles(cleanParams)

      const { records, current, size, total } = response.data

      if (current === 1) {
        articles.value = records
      } else {
        articles.value.push(...records)
      }

      pagination.value = { current, size, total }

      return { success: true }
    } catch (error) {
      console.error('获取热门文章失败:', error)
      return { success: false, message: '获取热门文章失败' }
    } finally {
      loading.value = false
    }
  }

  // 获取文章详情
  const fetchArticleDetail = async (id) => {
    try {
      loading.value = true
      const response = await getArticleById(id)
      currentArticle.value = response.data
      return { success: true }
    } catch (error) {
      console.error('获取文章详情失败:', error)
      return { success: false, message: '获取文章详情失败' }
    } finally {
      loading.value = false
    }
  }

  // 获取分类列表
  const fetchCategories = async () => {
    try {
      const response = await getCategories()
      categories.value = response.data
      return { success: true }
    } catch (error) {
      console.error('获取分类列表失败:', error)
      return { success: false, message: '获取分类列表失败' }
    }
  }

  // 搜索文章
  const searchArticleList = async (keyword, params = {}) => {
    try {
      loading.value = true
      const response = await searchArticles({
        keyword,
        current: 1,
        size: 20,
        ...params
      })
      
      searchResults.value = response.data.records
      return { success: true }
    } catch (error) {
      console.error('搜索文章失败:', error)
      return { success: false, message: '搜索文章失败' }
    } finally {
      loading.value = false
    }
  }

  // 加载更多文章
  const loadMoreArticles = async (params = {}) => {
    if (!hasMore.value || loading.value) return
    
    pagination.value.current += 1
    return await fetchArticles(params)
  }

  // 加载更多热门文章
  const loadMoreHotArticles = async (params = {}) => {
    if (!hasMore.value || loading.value) return
    
    pagination.value.current += 1
    return await fetchHotArticles(params)
  }

  // 重置分页
  const resetPagination = () => {
    pagination.value.current = 1
  }

  // 清空文章列表
  const clearArticles = () => {
    articles.value = []
    resetPagination()
  }

  return {
    // 状态
    articles,
    currentArticle,
    categories,
    hotArticles,
    searchResults,
    loading,
    pagination,
    hasMore,

    // 方法
    fetchArticles,
    fetchHotArticles,
    fetchArticleDetail,
    fetchCategories,
    searchArticleList,
    loadMoreArticles,
    loadMoreHotArticles,
    resetPagination,
    clearArticles
  }
})
