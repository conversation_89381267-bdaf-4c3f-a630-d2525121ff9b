import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import Cookies from 'js-cookie'
import { login, register, getUserInfo, logout } from '@/api/auth'
import { getAvatarUrl } from '@/utils/avatar'

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref(Cookies.get('token') || '')
  const userInfo = ref({})
  const loading = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => !!token.value)
  const avatar = computed(() => getAvatarUrl(userInfo.value.avatar))
  const nickname = computed(() => userInfo.value.nickname || userInfo.value.username || '游客')

  // 设置token
  const setToken = (newToken) => {
    token.value = newToken
    if (newToken) {
      Cookies.set('token', newToken, { expires: 7 }) // 7天过期
    } else {
      Cookies.remove('token')
    }
  }

  // 设置用户信息
  const setUserInfo = (info) => {
    userInfo.value = info
  }

  // 设置头像
  const setAvatar = (avatarUrl) => {
    userInfo.value.avatar = avatarUrl
  }

  // 登录
  const loginUser = async (loginForm) => {
    try {
      loading.value = true
      const response = await login(loginForm)
      const { token: newToken, user } = response.data
      
      setToken(newToken)
      setUserInfo(user)
      
      return { success: true, message: '登录成功' }
    } catch (error) {
      return { 
        success: false, 
        message: error.response?.data?.message || '登录失败' 
      }
    } finally {
      loading.value = false
    }
  }

  // 注册
  const registerUser = async (registerForm) => {
    try {
      loading.value = true
      const response = await register(registerForm)
      return { success: true, message: '注册成功，请登录' }
    } catch (error) {
      return { 
        success: false, 
        message: error.response?.data?.message || '注册失败' 
      }
    } finally {
      loading.value = false
    }
  }

  // 获取用户信息
  const fetchUserInfo = async () => {
    if (!token.value) return
    
    try {
      const response = await getUserInfo()
      setUserInfo(response.data)
    } catch (error) {
      console.error('获取用户信息失败:', error)
      // 如果token无效，清除登录状态
      if (error.response?.status === 401) {
        logoutUser()
      }
    }
  }

  // 登出
  const logoutUser = async (callBackend = true) => {
    try {
      if (callBackend && token.value) {
        await logout()
      }
    } catch (error) {
      console.error('登出失败:', error)
    } finally {
      setToken('')
      setUserInfo({})
    }
  }

  // 初始化用户信息
  const initUser = async () => {
    if (token.value) {
      await fetchUserInfo()
    }
  }

  return {
    // 状态
    token,
    userInfo,
    loading,
    
    // 计算属性
    isLoggedIn,
    avatar,
    nickname,
    
    // 方法
    setToken,
    setUserInfo,
    setAvatar,
    loginUser,
    registerUser,
    fetchUserInfo,
    logoutUser,
    initUser
  }
})
