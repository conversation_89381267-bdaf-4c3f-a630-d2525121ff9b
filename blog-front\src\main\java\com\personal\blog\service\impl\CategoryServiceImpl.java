package com.personal.blog.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.personal.blog.entity.Category;
import com.personal.blog.mapper.CategoryMapper;
import com.personal.blog.service.CategoryService;
import com.personal.blog.vo.CategoryVO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 分类Service实现类
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
@Service
public class CategoryServiceImpl extends ServiceImpl<CategoryMapper, Category> implements CategoryService {

    @Override
    public List<Category> getAllCategories() {
        return this.list(new LambdaQueryWrapper<Category>()
                .orderByAsc(Category::getSortOrder)
                .orderByAsc(Category::getId));
    }

    @Override
    public List<CategoryVO> getCategoriesWithArticleCount() {
        return baseMapper.selectCategoriesWithArticleCount();
    }

    @Override
    public List<CategoryVO> getHotCategories(Integer limit) {
        return baseMapper.selectHotCategories(limit);
    }
}
