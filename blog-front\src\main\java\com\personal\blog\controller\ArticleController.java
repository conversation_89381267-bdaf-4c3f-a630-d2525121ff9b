package com.personal.blog.controller;

import com.personal.blog.dto.ArticleCreateDTO;
import com.personal.blog.dto.ArticleQueryDTO;
import com.personal.blog.service.ArticleService;
import com.personal.blog.service.UserFavoriteService;
import com.personal.blog.service.UserLikeService;
import com.personal.blog.vo.ArticleVO;
import com.personal.blog.vo.PageResult;
import com.personal.blog.vo.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

import java.util.List;

/**
 * 文章控制器
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
@Tag(name = "文章管理", description = "文章相关接口")
@RestController
@RequestMapping("/api/articles")
@RequiredArgsConstructor
public class ArticleController {

    private final ArticleService articleService;
    private final UserLikeService userLikeService;
    private final UserFavoriteService userFavoriteService;

    @Operation(summary = "创建文章")
    @PostMapping
    public Result<ArticleVO> createArticle(@Valid @RequestBody ArticleCreateDTO articleCreateDTO) {
        ArticleVO articleVO = articleService.createArticle(articleCreateDTO);
        return Result.success("文章创建成功", articleVO);
    }

    @Operation(summary = "分页查询文章列表")
    @GetMapping
    public Result<PageResult<ArticleVO>> getArticlePage(ArticleQueryDTO queryDTO) {
        PageResult<ArticleVO> pageResult = articleService.getArticlePage(queryDTO);
        return Result.success(pageResult);
    }

    @Operation(summary = "根据ID查询文章详情")
    @GetMapping("/{id}")
    public Result<ArticleVO> getArticleById(@Parameter(description = "文章ID") @PathVariable Long id) {
        // 增加浏览量
        articleService.incrementViewCount(id);
        
        ArticleVO articleVO = articleService.getArticleById(id);
        return Result.success(articleVO);
    }

    @Operation(summary = "更新文章")
    @PutMapping("/{id}")
    public Result<ArticleVO> updateArticle(
            @Parameter(description = "文章ID") @PathVariable Long id,
            @Valid @RequestBody ArticleCreateDTO articleCreateDTO) {
        ArticleVO articleVO = articleService.updateArticle(id, articleCreateDTO);
        return Result.success("文章更新成功", articleVO);
    }

    @Operation(summary = "删除文章")
    @DeleteMapping("/{id}")
    public Result<String> deleteArticle(@Parameter(description = "文章ID") @PathVariable Long id) {
        articleService.deleteArticle(id);
        return Result.success("文章删除成功");
    }

    @Operation(summary = "点赞文章")
    @PostMapping("/{id}/like")
    public Result<String> likeArticle(@Parameter(description = "文章ID") @PathVariable Long id) {
        boolean success = userLikeService.likeArticle(id);
        return success ? Result.success("点赞成功") : Result.error("点赞失败");
    }

    @Operation(summary = "取消点赞")
    @DeleteMapping("/{id}/like")
    public Result<String> unlikeArticle(@Parameter(description = "文章ID") @PathVariable Long id) {
        boolean success = userLikeService.unlikeArticle(id);
        return success ? Result.success("取消点赞成功") : Result.error("取消点赞失败");
    }

    @Operation(summary = "收藏文章")
    @PostMapping("/{id}/favorite")
    public Result<String> favoriteArticle(@Parameter(description = "文章ID") @PathVariable Long id) {
        boolean success = userFavoriteService.favoriteArticle(id);
        return success ? Result.success("收藏成功") : Result.error("收藏失败");
    }

    @Operation(summary = "取消收藏")
    @DeleteMapping("/{id}/favorite")
    public Result<String> unfavoriteArticle(@Parameter(description = "文章ID") @PathVariable Long id) {
        boolean success = userFavoriteService.unfavoriteArticle(id);
        return success ? Result.success("取消收藏成功") : Result.error("取消收藏失败");
    }

    @Operation(summary = "检查是否已点赞")
    @GetMapping("/{id}/like")
    public Result<Boolean> checkLiked(@Parameter(description = "文章ID") @PathVariable Long id) {
        boolean isLiked = userLikeService.isLiked(id);
        return Result.success(isLiked);
    }

    @Operation(summary = "检查是否已收藏")
    @GetMapping("/{id}/favorite")
    public Result<Boolean> checkFavorited(@Parameter(description = "文章ID") @PathVariable Long id) {
        boolean favorited = userFavoriteService.isFavorited(id);
        return Result.success(favorited);
    }

    @Operation(summary = "获取作者文章列表")
    @GetMapping("/author/{authorId}")
    public Result<PageResult<ArticleVO>> getAuthorArticles(
            @Parameter(description = "作者ID") @PathVariable Long authorId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        ArticleQueryDTO queryDTO = new ArticleQueryDTO();
        queryDTO.setAuthorId(authorId);
        queryDTO.setCurrent((long) page);
        queryDTO.setSize((long) size);
        queryDTO.setIsPublished(1);
        
        PageResult<ArticleVO> pageResult = articleService.getArticlePage(queryDTO);
        return Result.success(pageResult);
    }

    @Operation(summary = "获取作者热门文章")
    @GetMapping("/author/{authorId}/popular")
    public Result<List<ArticleVO>> getAuthorPopularArticles(
            @Parameter(description = "作者ID") @PathVariable Long authorId,
            @RequestParam(defaultValue = "3") Integer limit) {
        List<ArticleVO> popularArticles = articleService.getAuthorPopularArticles(authorId, limit);
        return Result.success(popularArticles);
    }

    @Operation(summary = "获取热门文章")
    @GetMapping("/hot")
    public Result<PageResult<ArticleVO>> getHotArticles(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "12") Integer size) {
        PageResult<ArticleVO> pageResult = articleService.getHotArticles(current, size);
        return Result.success(pageResult);
    }
}
