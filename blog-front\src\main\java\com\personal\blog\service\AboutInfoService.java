package com.personal.blog.service;

import com.personal.blog.dto.AboutUpdateDTO;

import java.util.Map;

/**
 * 关于页面信息 Service 接口
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
public interface AboutInfoService {

    /**
     * 获取关于页面信息
     * @param userId 用户ID，可为null，null时获取当前登录用户信息
     */
    Map<String, Object> getAboutInfo(Long userId);

    /**
     * 更新关于页面信息
     */
    Map<String, Object> updateAboutInfo(AboutUpdateDTO aboutUpdateDTO);
} 