package com.personal.blog.config;

import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.router.SaRouter;
import cn.dev33.satoken.stp.StpUtil;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Sa-Token配置类
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
@Configuration
public class SaTokenConfig implements WebMvcConfigurer {

    /**
     * 注册Sa-Token拦截器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册Sa-Token拦截器，只对需要登录的接口进行校验
        registry.addInterceptor(new SaInterceptor(handle -> {
            SaRouter
                    // 只对需要登录的接口进行校验
                    .match("/api/user/like/**")     // 点赞相关
                    .match("/api/user/favorite/**") // 收藏相关
                    .match("/api/comments")         // 评论创建
                    .match("/api/comments/**")      // 评论删除、点赞等
                    .match("/api/user/profile/**")  // 用户信息相关
                    .match("/api/user/avatar/**")   // 头像上传
                    .match("/api/user/password/**") // 密码修改
                    .match("/api/about/info")       // 关于信息更新
                    .check(r -> StpUtil.checkLogin());

            // 权限校验 -- 管理员接口
            SaRouter.match("/api/admin/**", r -> StpUtil.checkRole("ADMIN"));
        })).addPathPatterns("/**");
    }
}
