package com.personal.blog.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.personal.blog.dto.AboutUpdateDTO;
import com.personal.blog.entity.AboutInfo;
import com.personal.blog.entity.User;
import com.personal.blog.mapper.AboutInfoMapper;
import com.personal.blog.mapper.UserMapper;
import com.personal.blog.service.AboutInfoService;
import cn.dev33.satoken.stp.StpUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 关于页面信息 Service 实现类
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AboutInfoServiceImpl implements AboutInfoService {

    private final AboutInfoMapper aboutInfoMapper;
    private final UserMapper userMapper;

    @Override
    public Map<String, Object> getAboutInfo(Long userId) {

        AboutInfo aboutInfo = getAboutInfoByUserId(userId);

        if (aboutInfo == null) {
            log.warn("未找到关于信息，userId: {}", userId);
            return null;
        }

        log.info("获取到关于信息: {}", aboutInfo);

        // 获取用户信息
        User user = userMapper.selectById(aboutInfo.getUserId());
        if (user == null) {
            log.warn("未找到用户信息，userId: {}", aboutInfo.getUserId());
            return null;
        }

        Map<String, Object> result = new HashMap<>();
        result.put("name", user.getNickname() != null ? user.getNickname() : user.getUsername());
        result.put("title", aboutInfo.getTitle());
        result.put("avatar", user.getAvatar() != null ? user.getAvatar() : "/default-avatar.png");
        result.put("description", aboutInfo.getDescription());

        // 联系方式
        Map<String, String> contact = new HashMap<>();
        contact.put("email", aboutInfo.getContactEmail());
        contact.put("github", aboutInfo.getContactGithub());
        contact.put("location", aboutInfo.getContactLocation());
        result.put("contact", contact);

        // 社交链接
        result.put("social", aboutInfo.getSocialLinks());

        // 技能信息
        result.put("skillsFrontend", aboutInfo.getSkillsFrontend());
        result.put("skillsBackend", aboutInfo.getSkillsBackend());
        result.put("skillsDatabase", aboutInfo.getSkillsDatabase());
        result.put("skillsTools", aboutInfo.getSkillsTools());

        log.info("返回关于信息结果: {}", result);
        return result;
    }

    /**
     * 根据用户ID获取关于信息
     */
    private AboutInfo getAboutInfoByUserId(Long userId) {
        // 使用原生SQL查询确保JSON字段正确解析
        AboutInfo aboutInfo = aboutInfoMapper.selectAboutInfoByUserId(userId);
        
        // 如果原生SQL查询失败，回退到MyBatis-Plus的自动映射
        if (aboutInfo == null) {
            LambdaQueryWrapper<AboutInfo> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(AboutInfo::getUserId, userId);
            aboutInfo = aboutInfoMapper.selectOne(wrapper);
        }
        
        return aboutInfo;
    }

    @Override
    @Transactional
    public Map<String, Object> updateAboutInfo(AboutUpdateDTO aboutUpdateDTO) {
        // 获取当前登录用户ID
        Long currentUserId = getCurrentUserId();
        if (currentUserId == null) {
            throw new RuntimeException("用户未登录");
        }
        
        // 查找当前用户的关于信息
        AboutInfo aboutInfo = getAboutInfoByUserId(currentUserId);
        
        if (aboutInfo == null) {
            // 如果不存在，创建新的记录，关联当前登录用户
            aboutInfo = new AboutInfo();
            aboutInfo.setUserId(currentUserId);
        }

        // 更新基本信息
        if (aboutUpdateDTO.getTitle() != null) {
            aboutInfo.setTitle(aboutUpdateDTO.getTitle());
        }
        if (aboutUpdateDTO.getDescription() != null) {
            aboutInfo.setDescription(aboutUpdateDTO.getDescription());
        }

        // 更新联系方式
        if (aboutUpdateDTO.getContact() != null) {
            if (aboutUpdateDTO.getContact().getEmail() != null) {
                aboutInfo.setContactEmail(aboutUpdateDTO.getContact().getEmail());
            }
            if (aboutUpdateDTO.getContact().getGithub() != null) {
                aboutInfo.setContactGithub(aboutUpdateDTO.getContact().getGithub());
            }
            if (aboutUpdateDTO.getContact().getLocation() != null) {
                aboutInfo.setContactLocation(aboutUpdateDTO.getContact().getLocation());
            }
        }

        // 更新社交链接
        if (aboutUpdateDTO.getSocial() != null) {
            // 将 SocialLink 对象转换为 Map
            List<Map<String, Object>> socialLinks = aboutUpdateDTO.getSocial().stream()
                .map(socialLink -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("name", socialLink.getName());
                    map.put("icon", socialLink.getIcon());
                    map.put("url", socialLink.getUrl());
                    return map;
                })
                .collect(java.util.stream.Collectors.toList());
            aboutInfo.setSocialLinks(socialLinks);
        }

        // 保存或更新
        if (aboutInfo.getId() == null) {
            aboutInfoMapper.insert(aboutInfo);
        } else {
            aboutInfoMapper.updateById(aboutInfo);
        }

        return getAboutInfo(null);
    }
    
    /**
     * 获取当前登录用户ID
     */
    private Long getCurrentUserId() {
        try {
            return StpUtil.getLoginIdAsLong();
        } catch (Exception e) {
            log.warn("获取当前用户ID失败", e);
            return null;
        }
    }
} 