package com.personal.blog.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.personal.blog.entity.FriendLink;
import com.personal.blog.mapper.FriendLinkMapper;
import com.personal.blog.service.FriendLinkService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 友情链接Service实现类
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
@Service
public class FriendLinkServiceImpl extends ServiceImpl<FriendLinkMapper, FriendLink> implements FriendLinkService {

    @Override
    public List<FriendLink> getEnabledFriendLinks() {
        return this.list(new LambdaQueryWrapper<FriendLink>()
                .eq(FriendLink::getIsEnabled, 1)
                .orderByAsc(FriendLink::getSortOrder)
                .orderByAsc(FriendLink::getId));
    }
} 