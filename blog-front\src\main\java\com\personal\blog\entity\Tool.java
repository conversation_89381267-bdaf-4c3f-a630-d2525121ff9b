package com.personal.blog.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 实用工具实体类
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("tb_tool")
public class Tool {

    /**
     * 工具ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 工具名称
     */
    @TableField("name")
    private String name;

    /**
     * 工具描述
     */
    @TableField("description")
    private String description;

    /**
     * 图标名称
     */
    @TableField("icon")
    private String icon;

    /**
     * 图标颜色
     */
    @TableField("icon_color")
    private String iconColor;

    /**
     * 动作类型
     */
    @TableField("action_type")
    private String actionType;

    /**
     * 动作参数
     */
    @TableField("action_params")
    private String actionParams;

    /**
     * 排序
     */
    @TableField("sort_order")
    private Integer sortOrder;

    /**
     * 是否启用
     */
    @TableField("is_enabled")
    private Integer isEnabled;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
} 