import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/store/user'
import { useAppStore } from '@/store/app'
import router from '@/router'

// 创建axios实例
const service = axios.create({
  baseURL: '/api', // 基础URL
  timeout: 10000, // 请求超时时间
  headers: {
    'Content-Type': 'application/json;charset=UTF-8'
  }
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    const userStore = useUserStore()
    const appStore = useAppStore()

    // 显示全局加载状态
    appStore.setLoading(true)

    // 添加token到请求头
    if (userStore.token) {
      config.headers['satoken'] = userStore.token
    }

    // 添加时间戳防止缓存
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }


    return config
  },
  error => {
    const appStore = useAppStore()
    appStore.setLoading(false)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    const appStore = useAppStore()
    appStore.setLoading(false)
    
    const { code, data, message } = response.data
    
    // 根据后端约定的状态码处理响应
    if (code === 200) {
      return { data, message }
    } else if (code === 401) {
      // token过期或无效
      const userStore = useUserStore()
      userStore.logoutUser(false) // 不调用后端logout接口
      router.push('/login')
      ElMessage.error('登录已过期，请重新登录')
      return Promise.reject(new Error(message || '登录已过期'))
    } else if (code === 403) {
      ElMessage.error('没有权限访问该资源')
      return Promise.reject(new Error(message || '没有权限'))
    } else {
      ElMessage.error(message || '请求失败')
      return Promise.reject(new Error(message || '请求失败'))
    }
  },
  error => {
    const appStore = useAppStore()
    appStore.setLoading(false)


    let message = '网络错误'
    
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 400:
          message = data.message || '请求参数错误'
          break
        case 401:
          message = '登录已过期，请重新登录'
          const userStore = useUserStore()
          userStore.logoutUser(false) // 不调用后端logout接口
          router.push('/login')
          break
        case 403:
          message = '没有权限访问该资源'
          break
        case 404:
          message = '请求的资源不存在'
          break
        case 500:
          message = '服务器内部错误'
          break
        case 502:
          message = '网关错误'
          break
        case 503:
          message = '服务不可用'
          break
        case 504:
          message = '网关超时'
          break
        default:
          message = data.message || `连接错误${status}`
      }
    } else if (error.code === 'ECONNABORTED') {
      message = '请求超时'
    } else if (error.message.includes('Network Error')) {
      message = '网络连接异常'
    }
    
    ElMessage.error(message)
    return Promise.reject(error)
  }
)

// 封装常用请求方法
export const request = {
  get(url, params) {
    return service.get(url, { params })
  },
  
  post(url, data) {
    return service.post(url, data)
  },
  
  put(url, data) {
    return service.put(url, data)
  },
  
  delete(url, params) {
    return service.delete(url, { params })
  },
  
  upload(url, formData) {
    return service.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }
}

export default service
