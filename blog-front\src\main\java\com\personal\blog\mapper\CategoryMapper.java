package com.personal.blog.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.personal.blog.entity.Category;
import com.personal.blog.vo.CategoryVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 分类Mapper接口
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
@Mapper
public interface CategoryMapper extends BaseMapper<Category> {

    /**
     * 获取所有分类及其文章数量
     * 
     * @return 分类列表
     */
    List<CategoryVO> selectCategoriesWithArticleCount();

    /**
     * 获取热门分类（文章数量最多的前N个）
     * 
     * @param limit 限制数量
     * @return 热门分类列表
     */
    List<CategoryVO> selectHotCategories(@Param("limit") Integer limit);
}
