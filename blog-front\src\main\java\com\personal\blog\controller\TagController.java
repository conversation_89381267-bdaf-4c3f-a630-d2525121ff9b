package com.personal.blog.controller;

import com.personal.blog.service.TagService;
import com.personal.blog.vo.Result;
import com.personal.blog.vo.TagVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 标签控制器
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
@Tag(name = "标签管理", description = "标签相关接口")
@RestController
@RequestMapping("/api/tags")
@RequiredArgsConstructor
public class TagController {

    private final TagService tagService;

    @Operation(summary = "获取热门标签")
    @GetMapping("/hot")
    public Result<List<TagVO>> getHotTags(
            @Parameter(description = "限制数量") @RequestParam(defaultValue = "10") Integer limit) {
        List<TagVO> hotTags = tagService.getHotTags(limit);
        return Result.success(hotTags);
    }
} 