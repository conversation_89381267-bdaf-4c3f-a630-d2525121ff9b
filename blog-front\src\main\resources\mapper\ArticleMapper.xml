<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.personal.blog.mapper.ArticleMapper">

    <!-- 文章VO结果映射 -->
    <resultMap id="ArticleVOMap" type="com.personal.blog.vo.ArticleVO">
        <id column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="summary" property="summary"/>
        <result column="content" property="content"/>
        <result column="cover_image" property="coverImage"/>
        <result column="category_id" property="categoryId"/>
        <result column="category_name" property="categoryName"/>
        <result column="author_id" property="authorId"/>
        <result column="author_nickname" property="authorNickname"/>
        <result column="author_avatar" property="authorAvatar"/>
        <result column="view_count" property="viewCount"/>
        <result column="like_count" property="likeCount"/>
        <result column="comment_count" property="commentCount"/>
        <result column="is_published" property="isPublished"/>
        <result column="is_top" property="isTop"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 分页查询文章列表 -->
    <select id="selectArticlePage" resultMap="ArticleVOMap">
        SELECT 
            a.id,
            a.title,
            a.summary,
            a.content,
            a.cover_image,
            a.category_id,
            c.name as category_name,
            a.author_id,
            u.nickname as author_nickname,
            u.avatar as author_avatar,
            a.view_count,
            a.like_count,
            a.comment_count,
            a.is_published,
            a.is_top,
            a.create_time,
            a.update_time
        FROM tb_article a
        LEFT JOIN tb_category c ON a.category_id = c.id
        LEFT JOIN tb_user u ON a.author_id = u.id
        <where>
            <if test="query.keyword != null and query.keyword != ''">
                AND (a.title LIKE CONCAT('%', #{query.keyword}, '%') 
                     OR a.content LIKE CONCAT('%', #{query.keyword}, '%'))
            </if>
            <if test="query.categoryId != null">
                AND a.category_id = #{query.categoryId}
            </if>
            <if test="query.isPublished != null">
                AND a.is_published = #{query.isPublished}
            </if>
            <if test="query.authorId != null">
                AND a.author_id = #{query.authorId}
            </if>
        </where>
        ORDER BY a.is_top DESC, a.create_time DESC
    </select>

    <!-- 查询文章标签 -->
    <select id="selectArticleTags" resultType="com.personal.blog.vo.TagVO">
        SELECT 
            t.id,
            t.name,
            t.description,
            t.color,
            t.sort_order,
            t.create_time,
            t.update_time
        FROM tb_article_tag at
        LEFT JOIN tb_tag t ON at.tag_id = t.id
        WHERE at.article_id = #{articleId}
        ORDER BY t.sort_order ASC, t.create_time ASC
    </select>

    <!-- 根据ID查询文章详情 -->
    <select id="selectArticleById" resultMap="ArticleVOMap">
        SELECT 
            a.id,
            a.title,
            a.summary,
            a.content,
            a.cover_image,
            a.category_id,
            c.name as category_name,
            a.author_id,
            u.nickname as author_nickname,
            u.avatar as author_avatar,
            a.view_count,
            a.like_count,
            a.comment_count,
            a.is_published,
            a.is_top,
            a.create_time,
            a.update_time
        FROM tb_article a
        LEFT JOIN tb_category c ON a.category_id = c.id
        LEFT JOIN tb_user u ON a.author_id = u.id
        WHERE a.id = #{id}
    </select>

    <!-- 查询用户点赞记录 -->
    <select id="selectUserLike" resultType="com.personal.blog.entity.UserLike">
        SELECT 
            id,
            user_id,
            article_id,
            status,
            create_time,
            update_time
        FROM tb_user_like
        WHERE user_id = #{userId} AND article_id = #{articleId}
        LIMIT 1
    </select>

    <!-- 查询用户收藏记录 -->
    <select id="selectUserFavorite" resultType="com.personal.blog.entity.UserFavorite">
        SELECT 
            id,
            user_id,
            article_id,
            status,
            create_time,
            update_time
        FROM tb_user_favorite
        WHERE user_id = #{userId} AND article_id = #{articleId}
        LIMIT 1
    </select>

    <!-- 查询用户文章列表 -->
    <select id="selectUserArticles" resultMap="ArticleVOMap">
        SELECT 
            a.id,
            a.title,
            a.summary,
            a.content,
            a.cover_image,
            a.category_id,
            c.name as category_name,
            a.author_id,
            u.nickname as author_nickname,
            u.avatar as author_avatar,
            a.view_count,
            a.like_count,
            a.comment_count,
            a.is_published,
            a.is_top,
            a.create_time,
            a.update_time
        FROM tb_article a
        LEFT JOIN tb_category c ON a.category_id = c.id
        LEFT JOIN tb_user u ON a.author_id = u.id
        WHERE a.author_id = #{userId}
        ORDER BY a.create_time DESC
    </select>

    <!-- 统计总访问量 -->
    <select id="selectTotalViewCount" resultType="java.lang.Integer">
        SELECT COALESCE(SUM(view_count), 0)
        FROM tb_article
        WHERE is_published = 1
    </select>

    <!-- 统计作者文章总访问量 -->
    <select id="selectAuthorTotalViewCount" resultType="java.lang.Integer">
        SELECT COALESCE(SUM(view_count), 0)
        FROM tb_article
        WHERE author_id = #{authorId} AND is_published = 1
    </select>

    <!-- 查询作者所有已发布的文章 -->
    <select id="selectAuthorArticles" resultMap="ArticleVOMap">
        SELECT 
            a.id,
            a.title,
            a.summary,
            a.content,
            a.cover_image,
            a.category_id,
            c.name as category_name,
            a.author_id,
            u.nickname as author_nickname,
            u.avatar as author_avatar,
            a.view_count,
            a.like_count,
            a.comment_count,
            a.is_published,
            a.is_top,
            a.create_time,
            a.update_time
        FROM tb_article a
        LEFT JOIN tb_category c ON a.category_id = c.id
        LEFT JOIN tb_user u ON a.author_id = u.id
        WHERE a.author_id = #{authorId} AND a.is_published = 1
        ORDER BY a.create_time DESC
    </select>

    <!-- 查询所有已发布的文章 -->
    <select id="selectAllPublishedArticles" resultMap="ArticleVOMap">
        SELECT 
            a.id,
            a.title,
            a.summary,
            a.content,
            a.cover_image,
            a.category_id,
            c.name as category_name,
            a.author_id,
            u.nickname as author_nickname,
            u.avatar as author_avatar,
            a.view_count,
            a.like_count,
            a.comment_count,
            a.is_published,
            a.is_top,
            a.create_time,
            a.update_time
        FROM tb_article a
        LEFT JOIN tb_category c ON a.category_id = c.id
        LEFT JOIN tb_user u ON a.author_id = u.id
        WHERE a.is_published = 1
        ORDER BY a.create_time DESC
    </select>

</mapper>
