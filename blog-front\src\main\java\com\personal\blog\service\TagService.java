package com.personal.blog.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.personal.blog.entity.Tag;
import com.personal.blog.vo.TagVO;

import java.util.List;

/**
 * 标签Service接口
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
public interface TagService extends IService<Tag> {

    /**
     * 获取所有标签及其文章数量
     * 
     * @return 标签列表
     */
    List<TagVO> getTagsWithArticleCount();

    /**
     * 获取热门标签（文章数量最多的前N个）
     * 
     * @param limit 限制数量
     * @return 热门标签列表
     */
    List<TagVO> getHotTags(Integer limit);
} 