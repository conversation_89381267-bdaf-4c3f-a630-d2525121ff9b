package com.personal.blog.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.personal.blog.entity.Category;
import com.personal.blog.vo.CategoryVO;

import java.util.List;

/**
 * 分类Service接口
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
public interface CategoryService extends IService<Category> {

    /**
     * 获取所有分类列表
     * 
     * @return 分类列表
     */
    List<Category> getAllCategories();

    /**
     * 获取所有分类及其文章数量
     * 
     * @return 分类列表
     */
    List<CategoryVO> getCategoriesWithArticleCount();

    /**
     * 获取热门分类（文章数量最多的前N个）
     * 
     * @param limit 限制数量
     * @return 热门分类列表
     */
    List<CategoryVO> getHotCategories(Integer limit);
}
