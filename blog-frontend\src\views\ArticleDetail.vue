<template>
  <div class="article-detail-page" v-loading="articleStore.loading">
    <div v-if="article.id" class="article-container">
      <!-- 文章头部 -->
      <header class="article-header">
        <div class="breadcrumb">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item
              v-if="article.category"
              :to="{ path: `/category/${article.category.id}` }"
            >
              {{ article.category.name }}
            </el-breadcrumb-item>
            <el-breadcrumb-item>{{ article.title }}</el-breadcrumb-item>
          </el-breadcrumb>
        </div>

        <h1 class="article-title">{{ article.title }}</h1>

        <div class="article-meta">
          <div class="meta-left">
            <div class="author-info">
              <img
                :src="getAvatarUrl(article.authorAvatar)"
                :alt="article.authorNickname"
                class="author-avatar"
              >
              <div class="author-details">
                <span class="author-name">
                  {{ article.authorNickname || '匿名' }}
                </span>
                <span class="publish-date">
                  发布于 {{ formatDate(article.createTime) }}
                </span>
              </div>
            </div>
          </div>

          <div class="meta-right">
            <div class="article-stats">
              <span class="stat-item">
                <el-icon><View /></el-icon>
                {{ article.viewCount || 0 }} 阅读
              </span>
              <span class="stat-item">
                <el-icon><ChatDotRound /></el-icon>
                {{ article.commentCount || 0 }} 评论
              </span>
              <span class="stat-item">
                <el-icon><Star /></el-icon>
                {{ article.likeCount || 0 }} 点赞
              </span>
            </div>
          </div>
        </div>

        <!-- 文章标签 -->
        <div class="article-tags" v-if="article.tags && article.tags.length">
          <el-tag
            v-for="tag in article.tags"
            :key="tag.id"
            class="tag-item"
            @click="$router.push({ path: '/search', query: { tag: tag.name } })"
          >
            {{ tag.name }}
          </el-tag>
        </div>
      </header>

      <!-- 文章内容 -->
      <main class="article-main">
        <div class="article-content" v-html="article.content"></div>
      </main>

      <!-- 文章操作 -->
      <div class="article-actions">
        <el-button
          :type="isLiked ? 'primary' : ''"
          :icon="Star"
          @click="toggleLike"
          :loading="likeLoading"
        >
          {{ isLiked ? '已点赞' : '点赞' }} ({{ article.likeCount || 0 }})
        </el-button>
        <el-button
          :type="isFavorited ? 'warning' : ''"
          :icon="Collection"
          @click="toggleFavorite"
          :loading="favoriteLoading"
        >
          {{ isFavorited ? '已收藏' : '收藏' }}
        </el-button>
        <el-button :icon="Share" @click="shareArticle">
          分享
        </el-button>
      </div>

      <!-- 评论区域 -->
      <div class="comments-section">
        <CommentList :article-id="article.id" />
      </div>
    </div>

    <!-- 文章不存在 -->
    <div v-else-if="!articleStore.loading" class="not-found">
      <el-result
        icon="warning"
        title="文章不存在"
        sub-title="抱歉，您访问的文章不存在或已被删除"
      >
        <template #extra>
          <el-button type="primary" @click="$router.push('/')">
            返回首页
          </el-button>
        </template>
      </el-result>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useArticleStore } from '@/store/article'
import { useUserStore } from '@/store/user'
import { 
  likeArticle, 
  unlikeArticle, 
  favoriteArticle, 
  unfavoriteArticle,
  checkLiked,
  checkFavorited
} from '@/api/article'
import { ElMessage } from 'element-plus'
import { Star, Collection, Share, View, ChatDotRound } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import CommentList from '@/components/comment/CommentList.vue'
import { getAvatarUrl } from '@/utils/avatar'

const route = useRoute()
const articleStore = useArticleStore()
const userStore = useUserStore()

const isLiked = ref(false)
const isFavorited = ref(false)
const likeLoading = ref(false)
const favoriteLoading = ref(false)

// 当前文章
const article = computed(() => articleStore.currentArticle)

// 格式化日期
const formatDate = (date) => {
  return dayjs(date).format('YYYY年MM月DD日 HH:mm')
}

// 切换点赞
const toggleLike = async () => {
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录')
    return
  }

  try {
    likeLoading.value = true
    if (isLiked.value) {
      await unlikeArticle(article.value.id)
      article.value.likeCount = Math.max(0, (article.value.likeCount || 0) - 1)
      isLiked.value = false
      ElMessage.success('取消点赞成功')
    } else {
      await likeArticle(article.value.id)
      article.value.likeCount = (article.value.likeCount || 0) + 1
      isLiked.value = true
      ElMessage.success('点赞成功')
    }
  } catch (error) {
    ElMessage.error('操作失败')
  } finally {
    likeLoading.value = false
  }
}

// 切换收藏
const toggleFavorite = async () => {
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录')
    return
  }

  try {
    favoriteLoading.value = true
    if (isFavorited.value) {
      await unfavoriteArticle(article.value.id)
      isFavorited.value = false
      ElMessage.success('取消收藏成功')
    } else {
      await favoriteArticle(article.value.id)
      isFavorited.value = true
      ElMessage.success('收藏成功')
    }
  } catch (error) {
    ElMessage.error('操作失败')
  } finally {
    favoriteLoading.value = false
  }
}

// 分享文章
const shareArticle = () => {
  if (navigator.share) {
    navigator.share({
      title: article.value.title,
      text: article.value.summary,
      url: window.location.href
    })
  } else {
    // 复制链接到剪贴板
    navigator.clipboard.writeText(window.location.href).then(() => {
      ElMessage.success('链接已复制到剪贴板')
    }).catch(() => {
      ElMessage.error('复制失败')
    })
  }
}

// 获取用户点赞和收藏状态
const fetchUserStatus = async () => {
  if (!userStore.isLoggedIn) {
    isLiked.value = false
    isFavorited.value = false
    return
  }

  try {
    console.log('获取用户状态，文章ID:', article.value.id)
    const [likedResponse, favoritedResponse] = await Promise.all([
      checkLiked(article.value.id),
      checkFavorited(article.value.id)
    ])
    
    console.log('点赞状态响应:', likedResponse)
    console.log('收藏状态响应:', favoritedResponse)
    
    isLiked.value = likedResponse.data
    isFavorited.value = favoritedResponse.data
    
    console.log('设置后的状态 - 点赞:', isLiked.value, '收藏:', isFavorited.value)
  } catch (error) {
    console.error('获取用户状态失败:', error)
    // 如果获取失败，设置为未点赞/未收藏状态
    isLiked.value = false
    isFavorited.value = false
  }
}

// 监听文章变化，重新获取用户状态
watch(() => article.value.id, async (newId) => {
  if (newId) {
    await fetchUserStatus()
  }
}, { immediate: false })

// 监听路由变化，重新加载文章
watch(() => route.params.id, async (newId) => {
  if (newId) {
    await articleStore.fetchArticleDetail(newId)
    
    // 设置页面标题
    if (article.value.title) {
      document.title = `${article.value.title} - 个人博客`
    }

    // 获取用户点赞和收藏状态
    await fetchUserStatus()
  }
}, { immediate: true })

// 初始化
onMounted(async () => {
  const articleId = route.params.id
  if (articleId) {
    await articleStore.fetchArticleDetail(articleId)

    // 设置页面标题
    if (article.value.title) {
      document.title = `${article.value.title} - 个人博客`
    }

    // 获取用户点赞和收藏状态
    await fetchUserStatus()
  }
})
</script>

<style scoped>
.article-detail-page {
  max-width: 100%;
}

.article-container {
  background: var(--bg-white);
  border-radius: var(--radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.article-header {
  padding: var(--spacing-xl);
  border-bottom: 1px solid var(--border-light);
}

.breadcrumb {
  margin-bottom: var(--spacing-lg);
}

.article-title {
  font-size: var(--font-3xl);
  font-weight: 700;
  color: var(--primary-color);
  line-height: 1.3;
  margin-bottom: var(--spacing-lg);
}

.article-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.author-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.author-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
}

.author-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.author-name {
  font-weight: 600;
  color: var(--primary-color);
}

.publish-date {
  font-size: var(--font-sm);
  color: var(--text-secondary);
}

.article-stats {
  display: flex;
  gap: var(--spacing-lg);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-sm);
  color: var(--text-secondary);
}

.article-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.tag-item {
  cursor: pointer;
  transition: all 0.3s ease;
}

.tag-item:hover {
  transform: translateY(-2px);
}

.article-main {
  padding: var(--spacing-xl);
}

.article-content {
  line-height: 1.8;
  font-size: var(--font-md);
  color: var(--text-primary);
}

/* 文章内容样式 */
.article-content :deep(h1),
.article-content :deep(h2),
.article-content :deep(h3),
.article-content :deep(h4),
.article-content :deep(h5),
.article-content :deep(h6) {
  margin: var(--spacing-xl) 0 var(--spacing-md) 0;
  font-weight: 600;
  color: var(--primary-color);
}

.article-content :deep(h1) { font-size: var(--font-2xl); }
.article-content :deep(h2) { font-size: var(--font-xl); }
.article-content :deep(h3) { font-size: var(--font-lg); }

.article-content :deep(p) {
  margin-bottom: var(--spacing-md);
}

.article-content :deep(blockquote) {
  border-left: 4px solid var(--accent-color);
  padding-left: var(--spacing-md);
  margin: var(--spacing-lg) 0;
  background: var(--bg-gray);
  padding: var(--spacing-md);
  border-radius: var(--radius-sm);
}

.article-content :deep(code) {
  background: var(--bg-gray);
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
}

.article-content :deep(pre) {
  background: var(--primary-color);
  color: white;
  padding: var(--spacing-md);
  border-radius: var(--radius-sm);
  overflow-x: auto;
  margin: var(--spacing-lg) 0;
}

.article-content :deep(pre code) {
  background: none;
  padding: 0;
  color: inherit;
}

.article-actions {
  padding: var(--spacing-lg) var(--spacing-xl);
  border-top: 1px solid var(--border-light);
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.comments-section {
  border-top: 1px solid var(--border-light);
  padding: var(--spacing-xl);
}

.not-found {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

/* 移动端样式 */
@media (max-width: 768px) {
  .article-header,
  .article-main,
  .comments-section {
    padding: var(--spacing-lg);
  }

  .article-actions {
    padding: var(--spacing-md) var(--spacing-lg);
  }

  .article-title {
    font-size: var(--font-2xl);
  }

  .article-meta {
    flex-direction: column;
    align-items: flex-start;
  }

  .article-stats {
    gap: var(--spacing-md);
  }

  .article-actions {
    flex-direction: column;
  }

  .article-actions .el-button {
    width: 100%;
  }
}
</style>
