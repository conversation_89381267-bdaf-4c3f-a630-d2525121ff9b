package com.personal.blog.controller;

import com.personal.blog.entity.Tool;
import com.personal.blog.service.ToolService;
import com.personal.blog.vo.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 实用工具控制器
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
@Tag(name = "实用工具管理", description = "实用工具相关接口")
@RestController
@RequestMapping("/api/tools")
@RequiredArgsConstructor
public class ToolController {

    private final ToolService toolService;

    @Operation(summary = "获取所有启用的实用工具")
    @GetMapping
    public Result<List<Tool>> getEnabledTools() {
        List<Tool> tools = toolService.getEnabledTools();
        return Result.success(tools);
    }
} 