package com.personal.blog.controller;

import com.personal.blog.entity.Category;
import com.personal.blog.service.CategoryService;
import com.personal.blog.vo.CategoryVO;
import com.personal.blog.vo.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 分类控制器
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
@Tag(name = "分类管理", description = "分类相关接口")
@RestController
@RequestMapping("/api/categories")
@RequiredArgsConstructor
public class CategoryController {

    private final CategoryService categoryService;

    @Operation(summary = "获取所有分类列表")
    @GetMapping
    public Result<List<Category>> getAllCategories() {
        List<Category> categories = categoryService.getAllCategories();
        return Result.success(categories);
    }

    @Operation(summary = "获取所有分类及其文章数量")
    @GetMapping("/with-count")
    public Result<List<CategoryVO>> getCategoriesWithArticleCount() {
        List<CategoryVO> categories = categoryService.getCategoriesWithArticleCount();
        return Result.success(categories);
    }

    @Operation(summary = "获取热门分类")
    @GetMapping("/hot")
    public Result<List<CategoryVO>> getHotCategories(
            @Parameter(description = "限制数量") @RequestParam(defaultValue = "5") Integer limit) {
        List<CategoryVO> hotCategories = categoryService.getHotCategories(limit);
        return Result.success(hotCategories);
    }
}
