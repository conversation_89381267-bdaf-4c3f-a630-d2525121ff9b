package com.personal.blog.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.personal.blog.entity.Tool;
import com.personal.blog.mapper.ToolMapper;
import com.personal.blog.service.ToolService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 实用工具Service实现类
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
@Service
public class ToolServiceImpl extends ServiceImpl<ToolMapper, Tool> implements ToolService {

    @Override
    public List<Tool> getEnabledTools() {
        return this.list(new LambdaQueryWrapper<Tool>()
                .eq(Tool::getIsEnabled, 1)
                .orderByAsc(Tool::getSortOrder)
                .orderByAsc(Tool::getId));
    }
} 