<template>
  <div class="about-page">

    
    <!-- 个人介绍 -->
    <div class="about-hero">
      <div class="hero-content">
        <div class="avatar-section">
          <img :src="getAvatarUrl(aboutInfo.avatar)" alt="头像" class="hero-avatar">
        </div>
        <div class="intro-section">
          <h1 class="hero-title">关于{{ aboutInfo.name || '博主' }}</h1>
          <p class="hero-subtitle">{{ aboutInfo.title || '正在加载...' }}</p>
          <div class="social-links" v-if="aboutInfo.social && aboutInfo.social.length > 0">
            <a 
              v-for="social in aboutInfo.social" 
              :key="social.name"
              :href="social.url" 
              class="social-link" 
              :title="social.name"
              target="_blank"
              rel="noopener noreferrer"
            >
              <el-icon><component :is="social.icon" /></el-icon>
              {{ social.name }}
            </a>
          </div>
          <div v-else>
            <p>暂无社交链接信息</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 详细介绍 -->
    <div class="about-content">
      <div class="content-section">
        <h2 class="section-title">
          <el-icon><User /></el-icon>
          个人简介
        </h2>
        <div class="section-content">
          <p v-if="!aboutInfo.description || aboutInfo.description.length === 0">
            暂无个人描述信息
          </p>
          <p v-else v-for="(paragraph, index) in aboutInfo.description" :key="index">
            {{ paragraph }}
          </p>
        </div>
      </div>

      <div class="content-section">
        <h2 class="section-title">
          <el-icon><Trophy /></el-icon>
          技能专长
        </h2>
        <div class="skills-grid" v-if="frontendSkills.length > 0 || backendSkills.length > 0 || databaseSkills.length > 0 || toolSkills.length > 0">
          <div class="skill-category" v-if="frontendSkills.length > 0">
            <h3>前端技术</h3>
            <div class="skill-tags">
              <el-tag v-for="skill in frontendSkills" :key="skill" class="skill-tag">
                {{ skill }}
              </el-tag>
            </div>
          </div>
          <div class="skill-category" v-if="backendSkills.length > 0">
            <h3>后端技术</h3>
            <div class="skill-tags">
              <el-tag v-for="skill in backendSkills" :key="skill" class="skill-tag" type="success">
                {{ skill }}
              </el-tag>
            </div>
          </div>
          <div class="skill-category" v-if="databaseSkills.length > 0">
            <h3>数据库</h3>
            <div class="skill-tags">
              <el-tag v-for="skill in databaseSkills" :key="skill" class="skill-tag" type="info">
                {{ skill }}
              </el-tag>
            </div>
          </div>
          <div class="skill-category" v-if="toolSkills.length > 0">
            <h3>工具/其他</h3>
            <div class="skill-tags">
              <el-tag v-for="skill in toolSkills" :key="skill" class="skill-tag" type="warning">
                {{ skill }}
              </el-tag>
            </div>
          </div>
        </div>
        <div v-else>
          <p>暂无技能信息</p>
        </div>
      </div>

      <div class="content-section">
        <h2 class="section-title">
          <el-icon><ChatDotRound /></el-icon>
          联系方式
        </h2>
        <div class="contact-info">
          <div class="contact-item" v-if="aboutInfo.contact && aboutInfo.contact.email">
            <el-icon><Message /></el-icon>
            <div class="contact-details">
              <strong>邮箱</strong>
              <span>{{ aboutInfo.contact.email }}</span>
            </div>
          </div>
          <div class="contact-item" v-if="aboutInfo.contact && aboutInfo.contact.github">
            <el-icon><Link /></el-icon>
            <div class="contact-details">
              <strong>GitHub</strong>
              <span>{{ aboutInfo.contact.github }}</span>
            </div>
          </div>
          <div class="contact-item" v-if="aboutInfo.contact && aboutInfo.contact.location">
            <el-icon><Location /></el-icon>
            <div class="contact-details">
              <strong>位置</strong>
              <span>{{ aboutInfo.contact.location }}</span>
            </div>
          </div>
          <div v-if="!aboutInfo.contact || (!aboutInfo.contact.email && !aboutInfo.contact.github && !aboutInfo.contact.location)">
            <p>暂无联系方式信息</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { getAvatarUrl } from '@/utils/avatar'
import { getAboutInfo } from '@/api/about'
import { ElMessage } from 'element-plus'

// 关于页面的信息
const aboutInfo = ref({})

// 获取关于页面信息
const fetchAboutInfo = async () => {
  try {
    const response = await getAboutInfo(1) // 固定获取管理员（用户ID为1）的信息
    
    if (response.data) {
      aboutInfo.value = response.data
      // 获取技能数据
      fetchSkillsData(response.data)
    } else {
      ElMessage.warning('暂无关于页面信息')
    }
  } catch (error) {
    console.error('获取关于页面信息失败:', error)
    ElMessage.error('获取关于页面信息失败')
  }
}

onMounted(() => {
  fetchAboutInfo()
})

// 技能数据
const frontendSkills = ref([])
const backendSkills = ref([])
const databaseSkills = ref([])
const toolSkills = ref([])

// 获取技能数据
const fetchSkillsData = (data) => {
  if (data.skillsFrontend) {
    frontendSkills.value = data.skillsFrontend
  }
  if (data.skillsBackend) {
    backendSkills.value = data.skillsBackend
  }
  if (data.skillsDatabase) {
    databaseSkills.value = data.skillsDatabase
  }
  if (data.skillsTools) {
    toolSkills.value = data.skillsTools
  }
}
</script>

<style scoped>
.about-page {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}



.about-hero {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  color: white;
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  text-align: center;
}

.hero-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-lg);
}

.hero-avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid rgba(255, 255, 255, 0.3);
  box-shadow: var(--shadow-lg);
}

.hero-title {
  font-size: var(--font-3xl);
  font-weight: 700;
  margin-bottom: var(--spacing-sm);
}

.hero-subtitle {
  font-size: var(--font-lg);
  opacity: 0.9;
  margin-bottom: var(--spacing-lg);
}

.social-links {
  display: flex;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
  justify-content: center;
}

.social-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: white;
  text-decoration: none;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--radius-sm);
  transition: all 0.3s ease;
}

.social-link:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.about-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.content-section {
  background: var(--bg-white);
  padding: var(--spacing-xl);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
}

.section-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-xl);
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-sm);
  border-bottom: 2px solid var(--border-light);
}

.section-content p {
  line-height: 1.8;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
}

.skill-category h3 {
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: var(--spacing-md);
}

.skill-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.skill-tag {
  cursor: default;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.contact-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--bg-gray);
  border-radius: var(--radius-sm);
}

.contact-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.contact-details strong {
  color: var(--primary-color);
  font-weight: 600;
}

.contact-details span {
  color: var(--text-secondary);
}

/* 移动端样式 */
@media (max-width: 768px) {
  .about-hero {
    padding: var(--spacing-lg);
  }

  .hero-avatar {
    width: 100px;
    height: 100px;
  }

  .hero-title {
    font-size: var(--font-2xl);
  }

  .social-links {
    gap: var(--spacing-md);
  }

  .content-section {
    padding: var(--spacing-lg);
  }

  .skills-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
}
</style>
