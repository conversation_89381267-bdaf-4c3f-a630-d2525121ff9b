package com.personal.blog.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.personal.blog.entity.Tag;
import com.personal.blog.vo.TagVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 标签Mapper接口
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
@Mapper
public interface TagMapper extends BaseMapper<Tag> {

    /**
     * 获取所有标签及其文章数量
     * 
     * @return 标签列表
     */
    List<TagVO> selectTagsWithArticleCount();

    /**
     * 获取热门标签（文章数量最多的前N个）
     * 
     * @param limit 限制数量
     * @return 热门标签列表
     */
    List<TagVO> selectHotTags(@Param("limit") Integer limit);
} 