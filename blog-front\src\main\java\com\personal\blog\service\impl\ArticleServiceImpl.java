package com.personal.blog.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.personal.blog.dto.ArticleCreateDTO;
import com.personal.blog.dto.ArticleQueryDTO;
import com.personal.blog.entity.Article;
import com.personal.blog.exception.BusinessException;
import com.personal.blog.mapper.ArticleMapper;
import com.personal.blog.service.ArticleService;
import com.personal.blog.utils.PopularityCalculator;
import com.personal.blog.vo.ArticleVO;
import com.personal.blog.vo.PageResult;
import com.personal.blog.vo.TagVO;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 文章Service实现类
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
@Service
@RequiredArgsConstructor
public class ArticleServiceImpl extends ServiceImpl<ArticleMapper, Article> implements ArticleService {

    @Override
    public ArticleVO createArticle(ArticleCreateDTO articleCreateDTO) {
        Long currentUserId = StpUtil.getLoginIdAsLong();
        
        Article article = BeanUtil.copyProperties(articleCreateDTO, Article.class);
        article.setAuthorId(currentUserId);
        article.setViewCount(0);
        article.setLikeCount(0);
        article.setCommentCount(0);
        
        // 如果没有提供摘要，从内容中截取
        if (StrUtil.isBlank(article.getSummary()) && StrUtil.isNotBlank(article.getContent())) {
            String summary = article.getContent().replaceAll("<[^>]*>", ""); // 去除HTML标签
            article.setSummary(summary.length() > 200 ? summary.substring(0, 200) + "..." : summary);
        }
        
        this.save(article);
        
        return baseMapper.selectArticleById(article.getId());
    }

    @Override
    public PageResult<ArticleVO> getArticlePage(ArticleQueryDTO queryDTO) {
        Page<ArticleVO> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());
        IPage<ArticleVO> result = baseMapper.selectArticlePage(page, queryDTO);
        
        // 为每篇文章添加标签信息
        for (ArticleVO article : result.getRecords()) {
            List<TagVO> tags = baseMapper.selectArticleTags(article.getId());
            article.setTags(tags);
        }
        
        return new PageResult<>(result.getRecords(), result.getTotal(), 
                result.getCurrent(), result.getSize());
    }

    @Override
    public ArticleVO getArticleById(Long id) {
        ArticleVO articleVO = baseMapper.selectArticleById(id);
        if (articleVO == null) {
            throw new BusinessException("文章不存在");
        }
        
        // 查询文章标签
        List<TagVO> tags = baseMapper.selectArticleTags(id);
        articleVO.setTags(tags);
        
        return articleVO;
    }

    @Override
    public ArticleVO updateArticle(Long id, ArticleCreateDTO articleCreateDTO) {
        Article existArticle = this.getById(id);
        if (existArticle == null) {
            throw new BusinessException("文章不存在");
        }
        
        // 检查权限
        Long currentUserId = StpUtil.getLoginIdAsLong();
        if (!existArticle.getAuthorId().equals(currentUserId)) {
            throw new BusinessException("无权限修改此文章");
        }
        
        Article article = BeanUtil.copyProperties(articleCreateDTO, Article.class);
        article.setId(id);
        
        // 如果没有提供摘要，从内容中截取
        if (StrUtil.isBlank(article.getSummary()) && StrUtil.isNotBlank(article.getContent())) {
            String summary = article.getContent().replaceAll("<[^>]*>", "");
            article.setSummary(summary.length() > 200 ? summary.substring(0, 200) + "..." : summary);
        }
        
        this.updateById(article);
        
        return baseMapper.selectArticleById(id);
    }

    @Override
    public void deleteArticle(Long id) {
        Article article = this.getById(id);
        if (article == null) {
            throw new BusinessException("文章不存在");
        }
        
        // 检查权限
        Long currentUserId = StpUtil.getLoginIdAsLong();
        if (!article.getAuthorId().equals(currentUserId)) {
            throw new BusinessException("无权限删除此文章");
        }
        
        this.removeById(id);
    }

    @Override
    public void incrementViewCount(Long id) {
        this.update(new LambdaUpdateWrapper<Article>()
                .eq(Article::getId, id)
                .setSql("view_count = view_count + 1"));
    }

    @Override
    public boolean isLiked(Long articleId) {
        Long currentUserId = StpUtil.getLoginIdAsLong();
        
        // 直接使用Mapper查询
        com.personal.blog.entity.UserLike userLike = baseMapper.selectUserLike(currentUserId, articleId);
        return userLike != null && userLike.getStatus() == 1;
    }

    @Override
    public boolean isFavorited(Long articleId) {
        Long currentUserId = StpUtil.getLoginIdAsLong();
        
        // 直接使用Mapper查询
        com.personal.blog.entity.UserFavorite userFavorite = baseMapper.selectUserFavorite(currentUserId, articleId);
        return userFavorite != null && userFavorite.getStatus() == 1;
    }

    @Override
    public PageResult<ArticleVO> getUserArticles(Integer current, Integer size) {
        Long currentUserId = StpUtil.getLoginIdAsLong();
        
        Page<ArticleVO> page = new Page<>(current, size);
        IPage<ArticleVO> result = baseMapper.selectUserArticles(page, currentUserId);
        
        return new PageResult<>(result.getRecords(), result.getTotal(), 
                result.getCurrent(), result.getSize());
    }

    @Override
    public List<ArticleVO> getAuthorPopularArticles(Long authorId, Integer limit) {
        // 获取作者所有已发布的文章
        List<ArticleVO> articles = baseMapper.selectAuthorArticles(authorId);
        
        // 使用热度计算工具进行排序和筛选
        return PopularityCalculator.getPopularArticles(articles, limit);
    }

    @Override
    public PageResult<ArticleVO> getHotArticles(Integer current, Integer size) {
        // 获取所有已发布的文章
        List<ArticleVO> allArticles = baseMapper.selectAllPublishedArticles();
        
        // 使用热度计算工具进行排序
        List<ArticleVO> sortedArticles = PopularityCalculator.calculateAndSortArticles(allArticles);
        
        // 手动分页
        int startIndex = (current - 1) * size;
        int endIndex = Math.min(startIndex + size, sortedArticles.size());
        
        List<ArticleVO> pageArticles = startIndex < sortedArticles.size() 
            ? sortedArticles.subList(startIndex, endIndex) 
            : List.of();
        
        return new PageResult<>(pageArticles, (long) sortedArticles.size(), (long) current, (long) size);
    }
}
