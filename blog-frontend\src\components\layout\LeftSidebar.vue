<template>
  <aside class="left-sidebar">
    <!-- 作者简介卡片 -->
    <div class="profile-card" v-if="authorInfo">
      <div class="profile-header">
        <div class="avatar-container">
          <img :src="authorInfo.avatar" :alt="authorInfo.nickname" class="avatar" />
          <div class="avatar-ring"></div>
        </div>
        <div class="profile-info">
          <h3 class="nickname">{{ authorInfo.nickname }}</h3>
          <p class="title" v-if="authorInfo.title">{{ authorInfo.title }}</p>
          <p class="about-title" v-if="aboutInfo.title">{{ aboutInfo.title }}</p>
        </div>
      </div>
      
      <!-- 联系方式 -->
      <div class="contact-info" v-if="aboutInfo.contact && (aboutInfo.contact.email || aboutInfo.contact.github || aboutInfo.contact.location)">
        <div class="contact-item" v-if="aboutInfo.contact.email">
          <el-icon><Message /></el-icon>
          <div class="contact-details">
            <strong>邮箱</strong>
            <span>{{ aboutInfo.contact.email }}</span>
          </div>
        </div>
        <div class="contact-item" v-if="aboutInfo.contact.github">
          <el-icon><Link /></el-icon>
          <div class="contact-details">
            <strong>GitHub</strong>
            <a :href="'https://' + aboutInfo.contact.github" target="_blank" class="contact-link">
              {{ aboutInfo.contact.github }}
            </a>
          </div>
        </div>
        <div class="contact-item" v-if="aboutInfo.contact.location">
          <el-icon><Location /></el-icon>
          <div class="contact-details">
            <strong>地址</strong>
            <span>{{ aboutInfo.contact.location }}</span>
          </div>
        </div>
      </div>
      
      <div class="profile-stats">
        <div class="stat-item">
          <span class="stat-number">{{ authorStats.articles }}</span>
          <span class="stat-label">文章</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ authorStats.views }}</span>
          <span class="stat-label">访问</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ authorStats.likes }}</span>
          <span class="stat-label">点赞</span>
        </div>
      </div>
    </div>

    <!-- 技能标签云 -->
    <div class="skills-card" v-if="skills.length > 0">
      <div class="card-header">
        <h4 class="card-title">
          <el-icon><Tools /></el-icon>
          技术栈
        </h4>
      </div>
      <div class="skills-cloud">
        <el-tag 
          v-for="skill in skills" 
          :key="skill.name"
          :type="skill.type"
          :effect="skill.effect"
          class="skill-tag"
          :class="skill.level"
        >
          {{ skill.name }}
        </el-tag>
      </div>
    </div>
  </aside>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useArticleStore } from '@/store/article'
import { getAuthorStatistics } from '@/api/statistics'
import { getAboutInfo } from '@/api/about'

const articleStore = useArticleStore()

// 作者信息
const authorInfo = ref(null)

// 关于信息
const aboutInfo = ref({})

// 作者统计数据
const authorStats = ref({
  articles: 0,
  views: 0,
  likes: 0
})

// 技能标签
const skills = ref([])

// 获取作者信息
const getAuthorInfo = async () => {
  try {
    // 从当前文章获取作者信息
    const currentArticle = articleStore.currentArticle
    if (currentArticle && currentArticle.authorId) {
      authorInfo.value = {
        id: currentArticle.authorId,
        nickname: currentArticle.authorNickname,
        avatar: currentArticle.authorAvatar
      }
      
      // 获取作者统计数据
      await getAuthorStats(currentArticle.authorId)
      
      // 获取作者关于信息
      await getAboutInfoData(currentArticle.authorId)
    }
  } catch (error) {
    console.error('获取作者信息失败:', error)
  }
}

// 获取作者统计数据
const getAuthorStats = async (authorId) => {
  try {
    const response = await getAuthorStatistics(authorId)
    const data = response.data
    
    authorStats.value = {
      articles: data.articleCount,
      views: data.viewCount,
      likes: data.likeCount
    }
  } catch (error) {
    console.error('获取作者统计数据失败:', error)
  }
}

// 获取作者关于信息
const getAboutInfoData = async (authorId) => {
  try {
    const response = await getAboutInfo(authorId)
    if (response.data) {
      aboutInfo.value = response.data
      
      // 处理技能数据
      processSkillsData(response.data)
    }
  } catch (error) {
    console.error('获取作者关于信息失败:', error)
  }
}

// 处理技能数据
const processSkillsData = (data) => {
  const allSkills = []
  
  if (data.skillsFrontend && data.skillsFrontend.length > 0) {
    data.skillsFrontend.forEach(skill => {
      allSkills.push({
        name: skill,
        type: 'primary',
        effect: 'light',
        level: 'expert'
      })
    })
  }
  
  if (data.skillsBackend && data.skillsBackend.length > 0) {
    data.skillsBackend.forEach(skill => {
      allSkills.push({
        name: skill,
        type: 'success',
        effect: 'light',
        level: 'advanced'
      })
    })
  }
  
  if (data.skillsDatabase && data.skillsDatabase.length > 0) {
    data.skillsDatabase.forEach(skill => {
      allSkills.push({
        name: skill,
        type: 'info',
        effect: 'light',
        level: 'intermediate'
      })
    })
  }
  
  if (data.skillsTools && data.skillsTools.length > 0) {
    data.skillsTools.forEach(skill => {
      allSkills.push({
        name: skill,
        type: 'warning',
        effect: 'light',
        level: 'intermediate'
      })
    })
  }
  
  skills.value = allSkills
}

// 监听文章变化
watch(() => articleStore.currentArticle, async (newArticle) => {
  if (newArticle && newArticle.id) {
    await getAuthorInfo()
  }
}, { immediate: true })

// 初始化数据
onMounted(async () => {
  await getAuthorInfo()
})
</script>

<style scoped>
.left-sidebar {
  width: 280px;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

/* 个人简介卡片 */
.profile-card {
  background: var(--bg-white);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
  transition: all 0.3s ease;
}

.profile-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.profile-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin-bottom: var(--spacing-lg);
}

.avatar-container {
  position: relative;
  margin-bottom: var(--spacing-md);
}

.avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid var(--primary-light);
  transition: all 0.3s ease;
}

.avatar-ring {
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  border: 2px solid transparent;
  border-radius: 50%;
  background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
  background-clip: padding-box;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.profile-card:hover .avatar-ring {
  opacity: 1;
}

.nickname {
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
}

.title {
  font-size: var(--font-sm);
  color: var(--text-secondary);
  margin: 0 0 var(--spacing-xs) 0;
}

.about-title {
  font-size: var(--font-sm);
  color: var(--primary-color);
  font-weight: 500;
  margin: 0 0 var(--spacing-md) 0;
}

.profile-bio {
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md);
  background: var(--bg-gray);
  border-radius: var(--radius-md);
  border-left: 3px solid var(--primary-color);
}

.bio-paragraph {
  font-size: var(--font-sm);
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0 0 var(--spacing-xs) 0;
}

.bio-paragraph:last-child {
  margin-bottom: 0;
}

.contact-info {
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md);
  background: var(--bg-gray);
  border-radius: var(--radius-md);
  border-left: 3px solid var(--primary-color);
}

.contact-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-xs);
}

.contact-item:last-child {
  margin-bottom: 0;
}

.contact-details {
  display: flex;
  flex-direction: column;
}

.contact-details strong {
  font-size: var(--font-sm);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.contact-details span {
  font-size: var(--font-xs);
  color: var(--text-secondary);
}

.contact-link {
  color: var(--text-secondary);
  text-decoration: none;
  transition: color 0.3s ease;
}

.contact-link:hover {
  color: var(--primary-color);
}

.profile-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md) 0;
  border-top: 1px solid var(--border-light);
  border-bottom: 1px solid var(--border-light);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
}

.stat-number {
  font-size: var(--font-lg);
  font-weight: 700;
  color: var(--primary-color);
}

.stat-label {
  font-size: var(--font-xs);
  color: var(--text-light);
}

/* 技能卡片 */
.skills-card {
  background: var(--bg-white);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
  transition: all 0.3s ease;
}

.skills-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.card-header {
  margin-bottom: var(--spacing-md);
}

.card-title {
  font-size: var(--font-md);
  font-weight: 600;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin: 0;
}

/* 技能标签云 */
.skills-cloud {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.skill-tag {
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: var(--font-xs);
  border-radius: var(--radius-md);
}

.skill-tag:hover {
  transform: scale(1.05);
}

.skill-tag.expert {
  font-weight: 600;
}

.skill-tag.advanced {
  font-weight: 500;
}

.skill-tag.intermediate {
  font-weight: 400;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .left-sidebar {
    width: 260px;
  }
}

@media (max-width: 1024px) {
  .left-sidebar {
    display: none;
  }
}
</style>
