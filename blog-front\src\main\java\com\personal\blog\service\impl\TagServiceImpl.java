package com.personal.blog.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.personal.blog.entity.Tag;
import com.personal.blog.mapper.TagMapper;
import com.personal.blog.service.TagService;
import com.personal.blog.vo.TagVO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 标签Service实现类
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
@Service
public class TagServiceImpl extends ServiceImpl<TagMapper, Tag> implements TagService {

    @Override
    public List<TagVO> getTagsWithArticleCount() {
        return baseMapper.selectTagsWithArticleCount();
    }

    @Override
    public List<TagVO> getHotTags(Integer limit) {
        return baseMapper.selectHotTags(limit);
    }
} 