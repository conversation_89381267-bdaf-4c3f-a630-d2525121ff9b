/*
 Navicat Premium Data Transfer

 Source Server         : Mysql
 Source Server Type    : MySQL
 Source Server Version : 80041
 Source Host           : localhost:3306
 Source Schema         : personal_blog

 Target Server Type    : MySQL
 Target Server Version : 80041
 File Encoding         : 65001

 Date: 31/07/2025 15:33:26
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for tb_about_info
-- ----------------------------
DROP TABLE IF EXISTS `tb_about_info`;
CREATE TABLE `tb_about_info`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '标题',
  `description` json NULL COMMENT '个人描述（JSON数组格式）',
  `contact_email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系邮箱',
  `contact_github` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'GitHub链接',
  `contact_location` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '位置信息',
  `social_links` json NULL COMMENT '社交链接（JSON数组格式）',
  `skills_frontend` json NULL COMMENT '前端技能（JSON数组格式）',
  `skills_backend` json NULL COMMENT '后端技能（JSON数组格式）',
  `skills_database` json NULL COMMENT '数据库技能（JSON数组格式）',
  `skills_tools` json NULL COMMENT '工具技能（JSON数组格式）',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  CONSTRAINT `fk_about_info_user` FOREIGN KEY (`user_id`) REFERENCES `tb_user` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '关于页面信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of tb_about_info
-- ----------------------------
INSERT INTO `tb_about_info` VALUES (1, 1, '全栈开发工程师 | 技术博主 | 开源爱好者', '[\"你好！我是一名热爱技术的全栈开发工程师，专注于Web开发和技术分享。\", \"拥有多年的开发经验，熟悉前端和后端技术栈。\", \"我喜欢学习新技术，探索最佳实践，并通过博客分享我的经验和见解。\", \"希望能够帮助更多的开发者成长，也期待与大家交流学习。\"]', '<EMAIL>', 'github.com/admin', '中国，北京', '[{\"url\": \"https://github.com/admin\", \"icon\": \"Link\", \"name\": \"GitHub\"}, {\"url\": \"mailto:<EMAIL>\", \"icon\": \"Message\", \"name\": \"Email\"}, {\"url\": \"https://weibo.com/admin\", \"icon\": \"Share\", \"name\": \"微博\"}]', '[\"Vue.js\", \"React\", \"JavaScript\", \"TypeScript\", \"HTML5\", \"CSS3\", \"Sass\", \"Element Plus\"]', '[\"Java\", \"Spring Boot\", \"Node.js\", \"Express\", \"Python\", \"Django\", \"RESTful API\"]', '[\"MySQL\", \"PostgreSQL\", \"MongoDB\", \"Redis\", \"Elasticsearch\"]', '[\"Git\", \"Docker\", \"Linux\", \"Nginx\", \"Webpack\", \"Vite\", \"Jenkins\"]', '2025-07-31 10:43:25', '2025-07-31 10:43:38');

-- ----------------------------
-- Table structure for tb_access_log
-- ----------------------------
DROP TABLE IF EXISTS `tb_access_log`;
CREATE TABLE `tb_access_log`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` bigint NULL DEFAULT NULL COMMENT '用户ID',
  `ip_address` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '访问IP',
  `location` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '访问地址',
  `request_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '请求URL',
  `request_method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '请求方法',
  `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '用户代理',
  `device_type` tinyint NULL DEFAULT 1 COMMENT '设备类型：1-PC，2-移动端，3-小程序',
  `operating_system` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '操作系统',
  `browser` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '浏览器',
  `access_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '访问时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_access_time`(`access_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '访问日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of tb_access_log
-- ----------------------------

-- ----------------------------
-- Table structure for tb_article
-- ----------------------------
DROP TABLE IF EXISTS `tb_article`;
CREATE TABLE `tb_article`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '文章ID',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '文章标题',
  `summary` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '文章摘要',
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '文章内容',
  `cover_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '封面图片',
  `category_id` bigint NULL DEFAULT NULL COMMENT '分类ID',
  `author_id` bigint NOT NULL COMMENT '作者ID',
  `view_count` int NULL DEFAULT 0 COMMENT '浏览量',
  `like_count` int NULL DEFAULT 0 COMMENT '点赞数',
  `comment_count` int NULL DEFAULT 0 COMMENT '评论数',
  `is_published` tinyint NULL DEFAULT 0 COMMENT '是否发布：1-已发布，0-草稿',
  `is_top` tinyint NULL DEFAULT 0 COMMENT '是否置顶',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_category_id`(`category_id` ASC) USING BTREE,
  INDEX `idx_author_id`(`author_id` ASC) USING BTREE,
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '文章表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of tb_article
-- ----------------------------
INSERT INTO `tb_article` VALUES (1, 'Spring Boot入门指南', 'Spring Boot是一个基于Spring框架的快速开发框架，本文将介绍如何快速上手Spring Boot开发。', '<h2>什么是Spring Boot</h2><p>Spring Boot是由Pivotal团队提供的全新框架，其设计目的是用来简化新Spring应用的初始搭建以及开发过程。</p><h2>主要特性</h2><ul><li>创建独立的Spring应用程序</li><li>嵌入的Tomcat，无需部署WAR文件</li><li>简化Maven配置</li><li>自动配置Spring</li></ul><h2>快速开始</h2><p>使用Spring Initializr可以快速创建一个Spring Boot项目...</p>', '/images/spring-boot-cover.jpg', 1, 1, 37, 1, 6, 1, 1, '2025-07-22 16:27:54', '2025-07-31 15:04:42');
INSERT INTO `tb_article` VALUES (2, 'Vue 3 Composition API详解', 'Vue 3引入了Composition API，这是一个全新的API设计，让我们能够更好地组织和复用代码逻辑。', '<h2>Composition API简介</h2><p>Composition API是Vue 3中最重要的新特性之一，它提供了一种更灵活的方式来组织组件逻辑。</p><h2>基本用法</h2><pre><code>import { ref, reactive } from \"vue\";\n\nexport default {\n  setup() {\n    const count = ref(0);\n    const state = reactive({ name: \"Vue 3\" });\n    \n    return { count, state };\n  }\n}</code></pre><h2>优势</h2><ul><li>更好的TypeScript支持</li><li>更灵活的逻辑复用</li><li>更好的代码组织</li></ul>', '/images/vue3-cover.jpg', 1, 1, 5, 0, 0, 1, 0, '2025-07-08 16:27:54', '2025-07-31 14:56:58');
INSERT INTO `tb_article` VALUES (3, 'MySQL性能优化实战', '本文将从索引优化、查询优化、配置优化等多个角度来讲解MySQL性能优化的实战技巧。', '<h2>索引优化</h2><p>索引是提高MySQL查询性能的最重要手段之一。</p><h3>索引类型</h3><ul><li>主键索引</li><li>唯一索引</li><li>普通索引</li><li>复合索引</li></ul><h2>查询优化</h2><p>编写高效的SQL语句是性能优化的关键...</p><h2>配置优化</h2><p>合理的MySQL配置可以显著提升数据库性能...</p>', '/images/mysql-cover.jpg', 1, 1, 0, 0, 0, 1, 0, '2025-07-30 16:27:54', '2025-07-31 11:56:56');
INSERT INTO `tb_article` VALUES (4, '生活中的小确幸', '记录生活中那些微小但美好的瞬间，感受平凡日子里的温暖与感动。', '<p>今天早上起床，阳光正好透过窗帘洒在床上，那一刻觉得特别温暖。</p><p>路过楼下的咖啡店，老板娘还记得我喜欢喝拿铁不加糖，这种被记住的感觉真好。</p><p>晚上和朋友视频聊天，虽然相隔千里，但友情依然如初。</p><p>这些看似平凡的小事，却构成了生活中最珍贵的回忆...</p>', '/images/life-cover.jpg', 2, 1, 6, 0, 0, 1, 0, '2025-07-02 16:27:54', '2025-07-31 14:57:03');
INSERT INTO `tb_article` VALUES (5, 'Docker容器化部署实践', '本文介绍如何使用Docker进行应用容器化部署，包括Dockerfile编写、镜像构建、容器运行等内容。', '<h2>Docker简介</h2><p>Docker是一个开源的容器化平台，可以让开发者打包应用以及依赖包到一个轻量级、可移植的容器中。</p><h2>Dockerfile编写</h2><pre><code>FROM openjdk:11-jre-slim\nCOPY target/app.jar app.jar\nEXPOSE 8080\nENTRYPOINT [\"java\", \"-jar\", \"/app.jar\"]</code></pre><h2>镜像构建</h2><p>使用docker build命令构建镜像...</p>', '/images/docker-cover.jpg', 1, 1, 0, 0, 0, 1, 0, '2025-07-30 16:27:54', '2025-07-31 11:56:59');
INSERT INTO `tb_article` VALUES (6, '读书笔记：《代码整洁之道》', '《代码整洁之道》是每个程序员都应该读的经典书籍，本文记录了我的读书心得和实践体会。', '<h2>什么是整洁代码</h2><p>整洁代码是能够被人轻松阅读和理解的代码。它不仅仅是能够运行的代码，更是优雅、简洁、易于维护的代码。</p><h2>命名的艺术</h2><p>好的命名能够让代码自文档化...</p><h2>函数设计原则</h2><ul><li>函数应该短小</li><li>只做一件事</li><li>使用描述性的名称</li></ul>', '/images/clean-code-cover.jpg', 3, 1, 0, 0, 0, 1, 0, '2025-07-30 16:27:54', '2025-07-31 13:09:29');
INSERT INTO `tb_article` VALUES (7, 'Redis缓存设计模式', '介绍Redis在实际项目中的应用模式，包括缓存穿透、缓存雪崩、缓存击穿等问题的解决方案。', '<h2>Redis基础</h2><p>Redis是一个开源的内存数据结构存储系统，可以用作数据库、缓存和消息代理。</p><h2>常见问题及解决方案</h2><h3>缓存穿透</h3><p>查询一个不存在的数据，由于缓存是不命中时被动写的，并且出于容错考虑，如果从存储层查不到数据则不写入缓存...</p>', '/images/redis-cover.jpg', 1, 1, 0, 0, 0, 1, 0, '2025-07-30 16:27:54', '2025-07-31 13:09:29');
INSERT INTO `tb_article` VALUES (8, '周末的慢时光', '忙碌的工作日结束了，周末是属于自己的时间，让我们一起享受这份难得的慢时光。', '<p>周六的早晨，没有闹钟的催促，自然醒来的感觉真好。</p><p>泡一壶好茶，翻开一本喜欢的书，时间仿佛都慢了下来。</p><p>下午去公园走走，看看绿树成荫，听听鸟儿歌唱，心情格外舒畅。</p><p>晚上在家做一顿丰盛的晚餐，享受烹饪的乐趣...</p>', '/images/weekend-cover.jpg', 2, 1, 0, 0, 0, 1, 0, '2025-07-30 16:27:54', '2025-07-31 13:09:29');
INSERT INTO `tb_article` VALUES (9, 'JavaScript异步编程进阶', '深入理解JavaScript异步编程，从回调函数到Promise，再到async/await的演进历程。', '<h2>异步编程的重要性</h2><p>JavaScript是单线程语言，异步编程是处理耗时操作的重要手段。</p><h2>回调函数</h2><p>最早的异步解决方案，但容易产生回调地狱...</p><h2>Promise</h2><p>ES6引入的Promise解决了回调地狱问题...</p><h2>async/await</h2><p>ES2017引入的语法糖，让异步代码看起来像同步代码...</p>', '/images/js-async-cover.jpg', 1, 1, 0, 0, 0, 1, 0, '2025-07-30 16:27:54', '2025-07-31 13:09:29');
INSERT INTO `tb_article` VALUES (10, '学习笔记：设计模式', '设计模式是软件开发中的重要概念，本文总结了常用的设计模式及其应用场景。', '<h2>什么是设计模式</h2><p>设计模式是在软件设计中常见问题的典型解决方案。</p><h2>创建型模式</h2><ul><li>单例模式</li><li>工厂模式</li><li>建造者模式</li></ul><h2>结构型模式</h2><ul><li>适配器模式</li><li>装饰器模式</li><li>代理模式</li></ul><h2>行为型模式</h2><ul><li>观察者模式</li><li>策略模式</li><li>命令模式</li></ul>', '/images/design-pattern-cover.jpg', 3, 1, 0, 0, 0, 1, 0, '2025-07-30 16:27:54', '2025-07-31 13:09:29');
INSERT INTO `tb_article` VALUES (11, '微服务架构实践', '介绍微服务架构的设计理念、技术选型和实施过程中的经验总结。', '<h2>微服务架构概述</h2><p>微服务架构是一种将单一应用程序开发为一组小型服务的方法。</p><h2>技术选型</h2><ul><li>Spring Cloud</li><li>Docker</li><li>Kubernetes</li><li>API Gateway</li></ul><h2>实施挑战</h2><p>服务拆分、数据一致性、分布式事务等问题需要仔细考虑...</p>', '/images/microservice-cover.jpg', 1, 1, 1, 0, 0, 1, 0, '2025-06-18 16:27:54', '2025-07-31 14:57:10');
INSERT INTO `tb_article` VALUES (12, '旅行日记：江南水乡', '记录一次江南水乡的旅行经历，感受古镇的宁静与美好。', '<p>这次江南之行，让我深深感受到了水乡古镇的独特魅力。</p><p>乌镇的小桥流水，西塘的烟雨朦胧，每一处风景都如诗如画。</p><p>漫步在青石板路上，听着脚步声在巷子里回响，仿佛穿越到了古代。</p><p>品尝当地的特色小吃，感受浓浓的人情味...</p>', '/images/jiangnan-cover.jpg', 2, 1, 0, 0, 0, 1, 0, '2025-07-30 16:27:54', '2025-07-31 13:09:29');

-- ----------------------------
-- Table structure for tb_article_category
-- ----------------------------
DROP TABLE IF EXISTS `tb_article_category`;
CREATE TABLE `tb_article_category`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `article_id` bigint NOT NULL COMMENT '文章ID',
  `category_id` bigint NOT NULL COMMENT '标签ID',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_article_id`(`article_id` ASC) USING BTREE,
  INDEX `idx_category_id`(`category_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 18 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '文章标签关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of tb_article_category
-- ----------------------------
INSERT INTO `tb_article_category` VALUES (1, 1, 2, '2025-07-30 16:29:44');
INSERT INTO `tb_article_category` VALUES (2, 1, 1, '2025-07-30 16:29:44');
INSERT INTO `tb_article_category` VALUES (3, 2, 3, '2025-07-30 16:29:44');
INSERT INTO `tb_article_category` VALUES (4, 3, 4, '2025-07-30 16:29:44');
INSERT INTO `tb_article_category` VALUES (5, 3, 1, '2025-07-30 16:29:44');
INSERT INTO `tb_article_category` VALUES (6, 4, 5, '2025-07-30 16:29:44');
INSERT INTO `tb_article_category` VALUES (7, 5, 1, '2025-07-30 16:29:44');
INSERT INTO `tb_article_category` VALUES (8, 5, 2, '2025-07-30 16:29:44');
INSERT INTO `tb_article_category` VALUES (9, 6, 1, '2025-07-30 16:29:44');
INSERT INTO `tb_article_category` VALUES (10, 7, 4, '2025-07-30 16:29:44');
INSERT INTO `tb_article_category` VALUES (11, 7, 1, '2025-07-30 16:29:44');
INSERT INTO `tb_article_category` VALUES (12, 8, 5, '2025-07-30 16:29:44');
INSERT INTO `tb_article_category` VALUES (13, 9, 3, '2025-07-30 16:29:44');
INSERT INTO `tb_article_category` VALUES (14, 10, 1, '2025-07-30 16:29:44');
INSERT INTO `tb_article_category` VALUES (15, 11, 1, '2025-07-30 16:29:44');
INSERT INTO `tb_article_category` VALUES (16, 11, 2, '2025-07-30 16:29:44');
INSERT INTO `tb_article_category` VALUES (17, 12, 5, '2025-07-30 16:29:44');

-- ----------------------------
-- Table structure for tb_article_tag
-- ----------------------------
DROP TABLE IF EXISTS `tb_article_tag`;
CREATE TABLE `tb_article_tag`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `article_id` bigint NOT NULL COMMENT '文章ID',
  `tag_id` bigint NOT NULL COMMENT '标签ID',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_article_id`(`article_id` ASC) USING BTREE,
  INDEX `idx_tag_id`(`tag_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 18 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '文章标签关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of tb_article_tag
-- ----------------------------
INSERT INTO `tb_article_tag` VALUES (1, 1, 6, '2025-07-31 15:21:06');
INSERT INTO `tb_article_tag` VALUES (2, 1, 7, '2025-07-31 15:21:06');
INSERT INTO `tb_article_tag` VALUES (3, 2, 1, '2025-07-31 15:21:06');
INSERT INTO `tb_article_tag` VALUES (4, 2, 3, '2025-07-31 15:21:06');
INSERT INTO `tb_article_tag` VALUES (5, 2, 4, '2025-07-31 15:21:06');
INSERT INTO `tb_article_tag` VALUES (6, 3, 7, '2025-07-31 15:21:06');
INSERT INTO `tb_article_tag` VALUES (7, 3, 8, '2025-07-31 15:21:06');
INSERT INTO `tb_article_tag` VALUES (8, 4, 3, '2025-07-31 15:21:06');
INSERT INTO `tb_article_tag` VALUES (9, 5, 6, '2025-07-31 15:21:06');
INSERT INTO `tb_article_tag` VALUES (10, 5, 9, '2025-07-31 15:21:06');
INSERT INTO `tb_article_tag` VALUES (11, 6, 3, '2025-07-31 15:21:06');
INSERT INTO `tb_article_tag` VALUES (12, 6, 10, '2025-07-31 15:21:06');
INSERT INTO `tb_article_tag` VALUES (13, 7, 8, '2025-07-31 15:21:06');
INSERT INTO `tb_article_tag` VALUES (14, 7, 6, '2025-07-31 15:21:06');
INSERT INTO `tb_article_tag` VALUES (15, 8, 3, '2025-07-31 15:21:06');
INSERT INTO `tb_article_tag` VALUES (16, 9, 3, '2025-07-31 15:21:06');
INSERT INTO `tb_article_tag` VALUES (17, 9, 4, '2025-07-31 15:21:06');
INSERT INTO `tb_article_tag` VALUES (18, 10, 3, '2025-07-31 15:21:06');
INSERT INTO `tb_article_tag` VALUES (19, 10, 10, '2025-07-31 15:21:06');
INSERT INTO `tb_article_tag` VALUES (20, 11, 6, '2025-07-31 15:21:06');
INSERT INTO `tb_article_tag` VALUES (21, 11, 9, '2025-07-31 15:21:06');
INSERT INTO `tb_article_tag` VALUES (22, 12, 3, '2025-07-31 15:21:06');

-- ----------------------------
-- Table structure for tb_category
-- ----------------------------
DROP TABLE IF EXISTS `tb_category`;
CREATE TABLE `tb_category`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '分类名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '分类描述',
  `parent_id` bigint NULL DEFAULT 0 COMMENT '父分类ID',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_parent_id`(`parent_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '分类表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of tb_category
-- ----------------------------
INSERT INTO `tb_category` VALUES (1, '技术分享', '技术相关的文章', 0, 1, '2025-07-30 14:51:30');
INSERT INTO `tb_category` VALUES (2, '生活随笔', '生活感悟和随笔', 0, 2, '2025-07-30 14:51:30');
INSERT INTO `tb_category` VALUES (3, '学习笔记', '学习过程中的笔记', 0, 3, '2025-07-30 14:51:30');

-- ----------------------------
-- Table structure for tb_comment
-- ----------------------------
DROP TABLE IF EXISTS `tb_comment`;
CREATE TABLE `tb_comment`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '评论ID',
  `article_id` bigint NOT NULL COMMENT '文章ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `parent_id` bigint NULL DEFAULT 0 COMMENT '父评论ID（第一层评论ID）',
  `reply_to_id` bigint NULL DEFAULT 0 COMMENT '回复目标评论ID（第二层回复的目标）',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '评论内容',
  `ip_address` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'IP地址',
  `like_count` int NULL DEFAULT 0 COMMENT '点赞数',
  `status` tinyint NULL DEFAULT 1 COMMENT '状态：1-正常，0-待审核，-1-已删除',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_article_id`(`article_id` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_parent_id`(`parent_id` ASC) USING BTREE,
  INDEX `idx_reply_to_id`(`reply_to_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '评论表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of tb_comment
-- ----------------------------
INSERT INTO `tb_comment` VALUES (1, 1, 15, 0, 0, '1231', '127.0.0.1', 1, 1, '2025-07-31 12:21:09');
INSERT INTO `tb_comment` VALUES (2, 1, 15, 1, 1, '12313', '127.0.0.1', 1, 1, '2025-07-31 12:21:15');

-- ----------------------------
-- Table structure for tb_comment_like
-- ----------------------------
DROP TABLE IF EXISTS `tb_comment_like`;
CREATE TABLE `tb_comment_like`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `comment_id` bigint NOT NULL COMMENT '评论ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_comment_user`(`comment_id` ASC, `user_id` ASC) USING BTREE,
  INDEX `idx_comment_id`(`comment_id` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '评论点赞表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of tb_comment_like
-- ----------------------------
INSERT INTO `tb_comment_like` VALUES (2, 2, 15, '2025-07-31 12:21:21');
INSERT INTO `tb_comment_like` VALUES (3, 1, 15, '2025-07-31 12:26:00');

-- ----------------------------
-- Table structure for tb_dict_data
-- ----------------------------
DROP TABLE IF EXISTS `tb_dict_data`;
CREATE TABLE `tb_dict_data`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '字典数据ID',
  `dict_type_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '字典类型编码',
  `dict_label` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '字典标签',
  `dict_value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '字典值',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '字典描述',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `is_default` tinyint NULL DEFAULT 0 COMMENT '是否默认：1-是，0-否',
  `css_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'CSS样式类',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_dict_type_code`(`dict_type_code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '字典数据表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of tb_dict_data
-- ----------------------------
INSERT INTO `tb_dict_data` VALUES (1, 'user_status', '正常', '1', '用户状态正常', 1, 1, 1, NULL, '2025-07-30 14:51:30', '2025-07-30 14:51:30');
INSERT INTO `tb_dict_data` VALUES (2, 'user_status', '禁用', '0', '用户被禁用', 2, 1, 0, NULL, '2025-07-30 14:51:30', '2025-07-30 14:51:30');
INSERT INTO `tb_dict_data` VALUES (3, 'article_status', '草稿', '0', '文章草稿状态', 1, 1, 0, NULL, '2025-07-30 14:51:30', '2025-07-30 14:51:30');
INSERT INTO `tb_dict_data` VALUES (4, 'article_status', '已发布', '1', '文章已发布', 2, 1, 1, NULL, '2025-07-30 14:51:30', '2025-07-30 14:51:30');
INSERT INTO `tb_dict_data` VALUES (5, 'comment_status', '待审核', '0', '评论待审核', 1, 1, 0, NULL, '2025-07-30 14:51:30', '2025-07-30 14:51:30');
INSERT INTO `tb_dict_data` VALUES (6, 'comment_status', '正常', '1', '评论正常显示', 2, 1, 1, NULL, '2025-07-30 14:51:30', '2025-07-30 14:51:30');
INSERT INTO `tb_dict_data` VALUES (7, 'comment_status', '已删除', '-1', '评论已删除', 3, 1, 0, NULL, '2025-07-30 14:51:30', '2025-07-30 14:51:30');
INSERT INTO `tb_dict_data` VALUES (8, 'device_type', 'PC端', '1', 'PC电脑端', 1, 1, 1, NULL, '2025-07-30 14:51:30', '2025-07-30 14:51:30');
INSERT INTO `tb_dict_data` VALUES (9, 'device_type', '移动端', '2', '手机移动端', 2, 1, 0, NULL, '2025-07-30 14:51:30', '2025-07-30 14:51:30');
INSERT INTO `tb_dict_data` VALUES (10, 'device_type', '小程序', '3', '微信小程序', 3, 1, 0, NULL, '2025-07-30 14:51:30', '2025-07-30 14:51:30');
INSERT INTO `tb_dict_data` VALUES (11, 'permission_type', '菜单', '1', '菜单权限', 1, 1, 1, NULL, '2025-07-30 14:51:30', '2025-07-30 14:51:30');
INSERT INTO `tb_dict_data` VALUES (12, 'permission_type', '按钮', '2', '按钮权限', 2, 1, 0, NULL, '2025-07-30 14:51:30', '2025-07-30 14:51:30');
INSERT INTO `tb_dict_data` VALUES (13, 'permission_type', '接口', '3', 'API接口权限', 3, 1, 0, NULL, '2025-07-30 14:51:30', '2025-07-30 14:51:30');

-- ----------------------------
-- Table structure for tb_dict_type
-- ----------------------------
DROP TABLE IF EXISTS `tb_dict_type`;
CREATE TABLE `tb_dict_type`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '字典类型ID',
  `dict_type_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '字典类型编码',
  `dict_type_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '字典类型名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '字典类型描述',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_dict_type_code`(`dict_type_code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '字典类型表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of tb_dict_type
-- ----------------------------
INSERT INTO `tb_dict_type` VALUES (1, 'user_status', '用户状态', '用户账号状态', 1, 1, '2025-07-30 14:51:30', '2025-07-30 14:51:30');
INSERT INTO `tb_dict_type` VALUES (2, 'article_status', '文章状态', '文章发布状态', 2, 1, '2025-07-30 14:51:30', '2025-07-30 14:51:30');
INSERT INTO `tb_dict_type` VALUES (3, 'comment_status', '评论状态', '评论审核状态', 3, 1, '2025-07-30 14:51:30', '2025-07-30 14:51:30');
INSERT INTO `tb_dict_type` VALUES (4, 'device_type', '设备类型', '访问设备类型', 4, 1, '2025-07-30 14:51:30', '2025-07-30 14:51:30');
INSERT INTO `tb_dict_type` VALUES (5, 'permission_type', '权限类型', '权限分类类型', 5, 1, '2025-07-30 14:51:30', '2025-07-30 14:51:30');

-- ----------------------------
-- Table structure for tb_friend_link
-- ----------------------------
DROP TABLE IF EXISTS `tb_friend_link`;
CREATE TABLE `tb_friend_link`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '链接ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '链接名称',
  `description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '链接描述',
  `url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '链接地址',
  `avatar` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '头像地址',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序',
  `is_enabled` tinyint NULL DEFAULT 1 COMMENT '是否启用(0:禁用 1:启用)',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '友情链接表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of tb_friend_link
-- ----------------------------
INSERT INTO `tb_friend_link` VALUES (1, 'Vue.js 官网', '渐进式 JavaScript 框架', 'https://vuejs.org', 'https://vuejs.org/logo.svg', 1, 1, '2025-07-31 15:21:06', '2025-07-31 15:21:06');
INSERT INTO `tb_friend_link` VALUES (2, 'Element Plus', 'Vue 3 组件库', 'https://element-plus.org', 'https://element-plus.org/images/element-plus-logo-small.svg', 2, 1, '2025-07-31 15:21:06', '2025-07-31 15:21:06');
INSERT INTO `tb_friend_link` VALUES (3, 'GitHub', '代码托管平台', 'https://github.com', 'https://github.githubassets.com/images/modules/logos_page/GitHub-Mark.png', 3, 1, '2025-07-31 15:21:06', '2025-07-31 15:21:06');
INSERT INTO `tb_friend_link` VALUES (4, 'Spring 官网', 'Java 应用框架', 'https://spring.io', 'https://spring.io/images/spring-logo.svg', 4, 1, '2025-07-31 15:21:06', '2025-07-31 15:21:06');
INSERT INTO `tb_friend_link` VALUES (5, 'MyBatis Plus', 'MyBatis 增强工具', 'https://baomidou.com', 'https://baomidou.com/img/logo.svg', 5, 1, '2025-07-31 15:21:06', '2025-07-31 15:21:06');

-- ----------------------------
-- Table structure for tb_permission
-- ----------------------------
DROP TABLE IF EXISTS `tb_permission`;
CREATE TABLE `tb_permission`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `permission_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '权限编码',
  `permission_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '权限名称',
  `permission_type` tinyint NULL DEFAULT 1 COMMENT '权限类型：1-菜单，2-按钮，3-接口',
  `parent_id` bigint NULL DEFAULT 0 COMMENT '父权限ID',
  `permission_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '权限路径',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '权限描述',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_permission_code`(`permission_code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '权限表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of tb_permission
-- ----------------------------
INSERT INTO `tb_permission` VALUES (1, 'SYSTEM', '系统管理', 1, 0, '/admin/system', '系统管理菜单', 1, 1, '2025-07-30 14:51:30', '2025-07-30 14:51:30');
INSERT INTO `tb_permission` VALUES (2, 'SYSTEM:USER', '用户管理', 1, 1, '/admin/system/user', '用户管理页面', 1, 1, '2025-07-30 14:51:30', '2025-07-30 14:51:30');
INSERT INTO `tb_permission` VALUES (3, 'SYSTEM:ROLE', '角色管理', 1, 1, '/admin/system/role', '角色管理页面', 2, 1, '2025-07-30 14:51:30', '2025-07-30 14:51:30');
INSERT INTO `tb_permission` VALUES (4, 'SYSTEM:PERMISSION', '权限管理', 1, 1, '/admin/system/permission', '权限管理页面', 3, 1, '2025-07-30 14:51:30', '2025-07-30 14:51:30');
INSERT INTO `tb_permission` VALUES (5, 'CONTENT', '内容管理', 1, 0, '/admin/content', '内容管理菜单', 2, 1, '2025-07-30 14:51:30', '2025-07-30 14:51:30');
INSERT INTO `tb_permission` VALUES (6, 'CONTENT:ARTICLE', '文章管理', 1, 5, '/admin/content/article', '文章管理页面', 1, 1, '2025-07-30 14:51:30', '2025-07-30 14:51:30');
INSERT INTO `tb_permission` VALUES (7, 'CONTENT:CATEGORY', '分类管理', 1, 5, '/admin/content/category', '分类管理页面', 2, 1, '2025-07-30 14:51:30', '2025-07-30 14:51:30');
INSERT INTO `tb_permission` VALUES (8, 'CONTENT:TAG', '标签管理', 1, 5, '/admin/content/tag', '标签管理页面', 3, 1, '2025-07-30 14:51:30', '2025-07-30 14:51:30');
INSERT INTO `tb_permission` VALUES (9, 'CONTENT:COMMENT', '评论管理', 1, 5, '/admin/content/comment', '评论管理页面', 4, 1, '2025-07-30 14:51:30', '2025-07-30 14:51:30');

-- ----------------------------
-- Table structure for tb_role
-- ----------------------------
DROP TABLE IF EXISTS `tb_role`;
CREATE TABLE `tb_role`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色编码',
  `role_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '角色描述',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_role_code`(`role_code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '角色表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of tb_role
-- ----------------------------
INSERT INTO `tb_role` VALUES (1, 'ADMIN', '超级管理员', '系统超级管理员，拥有所有权限', 1, 1, '2025-07-30 14:51:30', '2025-07-30 14:51:30');
INSERT INTO `tb_role` VALUES (2, 'EDITOR', '编辑员', '内容编辑员，可以管理文章和评论', 2, 1, '2025-07-30 14:51:30', '2025-07-30 14:51:30');
INSERT INTO `tb_role` VALUES (3, 'USER', '普通用户', '普通注册用户', 3, 1, '2025-07-30 14:51:30', '2025-07-30 14:51:30');

-- ----------------------------
-- Table structure for tb_role_permission
-- ----------------------------
DROP TABLE IF EXISTS `tb_role_permission`;
CREATE TABLE `tb_role_permission`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `permission_id` bigint NOT NULL COMMENT '权限ID',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_role_id`(`role_id` ASC) USING BTREE,
  INDEX `idx_permission_id`(`permission_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '角色权限关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of tb_role_permission
-- ----------------------------
INSERT INTO `tb_role_permission` VALUES (1, 1, 1, '2025-07-30 14:51:30');
INSERT INTO `tb_role_permission` VALUES (2, 1, 2, '2025-07-30 14:51:30');
INSERT INTO `tb_role_permission` VALUES (3, 1, 3, '2025-07-30 14:51:30');
INSERT INTO `tb_role_permission` VALUES (4, 1, 4, '2025-07-30 14:51:30');
INSERT INTO `tb_role_permission` VALUES (5, 1, 5, '2025-07-30 14:51:30');
INSERT INTO `tb_role_permission` VALUES (6, 1, 6, '2025-07-30 14:51:30');
INSERT INTO `tb_role_permission` VALUES (7, 1, 7, '2025-07-30 14:51:30');
INSERT INTO `tb_role_permission` VALUES (8, 1, 8, '2025-07-30 14:51:30');
INSERT INTO `tb_role_permission` VALUES (9, 1, 9, '2025-07-30 14:51:30');

-- ----------------------------
-- Table structure for tb_system_config
-- ----------------------------
DROP TABLE IF EXISTS `tb_system_config`;
CREATE TABLE `tb_system_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '配置键',
  `config_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '配置值',
  `config_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '配置名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '配置描述',
  `config_type` tinyint NULL DEFAULT 1 COMMENT '配置类型：1-系统配置，2-业务配置',
  `is_builtin` tinyint NULL DEFAULT 0 COMMENT '是否内置：1-是，0-否',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_config_key`(`config_key` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '系统配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of tb_system_config
-- ----------------------------
INSERT INTO `tb_system_config` VALUES (1, 'blog.title', '个人博客系统', '博客标题', '网站标题', 2, 1, 1, '2025-07-30 14:51:30', '2025-07-30 14:51:30');
INSERT INTO `tb_system_config` VALUES (2, 'blog.subtitle', '记录生活，分享技术', '博客副标题', '网站副标题', 2, 1, 2, '2025-07-30 14:51:30', '2025-07-30 14:51:30');
INSERT INTO `tb_system_config` VALUES (3, 'blog.description', '这是一个基于Spring Boot的个人博客系统', '博客描述', '网站描述', 2, 1, 3, '2025-07-30 14:51:30', '2025-07-30 14:51:30');
INSERT INTO `tb_system_config` VALUES (4, 'blog.keywords', '个人博客,技术分享,生活随笔', '博客关键词', '网站关键词', 2, 1, 4, '2025-07-30 14:51:30', '2025-07-30 14:51:30');
INSERT INTO `tb_system_config` VALUES (5, 'blog.author', '博主', '博客作者', '博客作者名称', 2, 1, 5, '2025-07-30 14:51:30', '2025-07-30 14:51:30');
INSERT INTO `tb_system_config` VALUES (6, 'blog.email', '<EMAIL>', '联系邮箱', '博主联系邮箱', 2, 1, 6, '2025-07-30 14:51:30', '2025-07-30 14:51:30');
INSERT INTO `tb_system_config` VALUES (7, 'blog.icp', '', 'ICP备案号', '网站ICP备案号', 2, 1, 7, '2025-07-30 14:51:30', '2025-07-30 14:51:30');
INSERT INTO `tb_system_config` VALUES (8, 'seo.title_suffix', ' - 个人博客', 'SEO标题后缀', 'SEO页面标题后缀', 2, 1, 10, '2025-07-30 14:51:30', '2025-07-30 14:51:30');
INSERT INTO `tb_system_config` VALUES (9, 'seo.meta_description', '个人博客系统，记录生活，分享技术', 'SEO描述', 'SEO页面描述', 2, 1, 11, '2025-07-30 14:51:30', '2025-07-30 14:51:30');
INSERT INTO `tb_system_config` VALUES (10, 'seo.meta_keywords', '个人博客,技术分享,生活随笔,Spring Boot', 'SEO关键词', 'SEO页面关键词', 2, 1, 12, '2025-07-30 14:51:30', '2025-07-30 14:51:30');
INSERT INTO `tb_system_config` VALUES (11, 'system.comment_audit', '0', '评论审核', '是否开启评论审核：1-开启，0-关闭', 1, 1, 20, '2025-07-30 14:51:30', '2025-07-30 14:51:30');
INSERT INTO `tb_system_config` VALUES (12, 'system.register_enabled', '1', '用户注册', '是否允许用户注册：1-允许，0-禁止', 1, 1, 21, '2025-07-30 14:51:30', '2025-07-30 14:51:30');
INSERT INTO `tb_system_config` VALUES (13, 'system.upload_max_size', '10485760', '上传文件大小限制', '上传文件最大大小（字节）', 1, 1, 22, '2025-07-30 14:51:30', '2025-07-30 14:51:30');
INSERT INTO `tb_system_config` VALUES (14, 'system.page_size', '10', '分页大小', '默认分页大小', 1, 1, 23, '2025-07-30 14:51:30', '2025-07-30 14:51:30');

-- ----------------------------
-- Table structure for tb_tag
-- ----------------------------
DROP TABLE IF EXISTS `tb_tag`;
CREATE TABLE `tb_tag`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '标签ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '标签名称',
  `description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '标签描述',
  `color` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '标签颜色',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_name`(`name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '标签表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of tb_tag
-- ----------------------------
INSERT INTO `tb_tag` VALUES (1, 'Vue.js', '渐进式 JavaScript 框架', '#42b883', 1, '2025-07-31 15:21:06', '2025-07-31 15:21:06');
INSERT INTO `tb_tag` VALUES (2, 'React', '用于构建用户界面的 JavaScript 库', '#61dafb', 2, '2025-07-31 15:21:06', '2025-07-31 15:21:06');
INSERT INTO `tb_tag` VALUES (3, 'JavaScript', '动态编程语言', '#f7df1e', 3, '2025-07-31 15:21:06', '2025-07-31 15:21:06');
INSERT INTO `tb_tag` VALUES (4, 'TypeScript', 'JavaScript 的超集', '#3178c6', 4, '2025-07-31 15:21:06', '2025-07-31 15:21:06');
INSERT INTO `tb_tag` VALUES (5, 'Node.js', 'JavaScript 运行时', '#339933', 5, '2025-07-31 15:21:06', '2025-07-31 15:21:06');
INSERT INTO `tb_tag` VALUES (6, 'Spring Boot', 'Java 应用框架', '#6db33f', 6, '2025-07-31 15:21:06', '2025-07-31 15:21:06');
INSERT INTO `tb_tag` VALUES (7, 'MySQL', '关系型数据库', '#4479a1', 7, '2025-07-31 15:21:06', '2025-07-31 15:21:06');
INSERT INTO `tb_tag` VALUES (8, 'Redis', '内存数据库', '#dc382d', 8, '2025-07-31 15:21:06', '2025-07-31 15:21:06');
INSERT INTO `tb_tag` VALUES (9, 'Docker', '容器化平台', '#2496ed', 9, '2025-07-31 15:21:06', '2025-07-31 15:21:06');
INSERT INTO `tb_tag` VALUES (10, 'Git', '版本控制系统', '#f05032', 10, '2025-07-31 15:21:06', '2025-07-31 15:21:06');

-- ----------------------------
-- Table structure for tb_tool
-- ----------------------------
DROP TABLE IF EXISTS `tb_tool`;
CREATE TABLE `tb_tool`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '工具ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '工具名称',
  `description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '工具描述',
  `icon` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '图标名称',
  `icon_color` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '图标颜色',
  `action_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '动作类型',
  `action_params` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '动作参数',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序',
  `is_enabled` tinyint NULL DEFAULT 1 COMMENT '是否启用(0:禁用 1:启用)',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '实用工具表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of tb_tool
-- ----------------------------
INSERT INTO `tb_tool` VALUES (1, '文章搜索', '快速查找文章', 'Search', 'text-blue', 'search', '', 1, 1, '2025-07-31 15:21:06', '2025-07-31 15:21:06');
INSERT INTO `tb_tool` VALUES (2, '随机文章', '发现新内容', 'Refresh', 'text-green', 'random', '', 2, 1, '2025-07-31 15:21:06', '2025-07-31 15:21:06');
INSERT INTO `tb_tool` VALUES (3, '文章归档', '按时间浏览', 'Calendar', 'text-orange', 'archive', '', 3, 1, '2025-07-31 15:21:06', '2025-07-31 15:21:06');
INSERT INTO `tb_tool` VALUES (4, 'RSS 订阅', '订阅更新', 'Promotion', 'text-purple', 'rss', '', 4, 1, '2025-07-31 15:21:06', '2025-07-31 15:21:06');
INSERT INTO `tb_tool` VALUES (5, '返回顶部', '快速回到顶部', 'Top', 'text-red', 'scroll', 'top', 5, 1, '2025-07-31 15:21:06', '2025-07-31 15:21:06');

-- ----------------------------
-- Table structure for tb_user
-- ----------------------------
DROP TABLE IF EXISTS `tb_user`;
CREATE TABLE `tb_user`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户名',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '邮箱',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '密码',
  `nickname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '头像URL',
  `role` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'USER' COMMENT '角色',
  `status` tinyint NULL DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_username`(`username` ASC) USING BTREE,
  UNIQUE INDEX `uk_email`(`email` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of tb_user
-- ----------------------------
INSERT INTO `tb_user` VALUES (1, 'admin', '<EMAIL>', '$10$.chN8DWbY2DVsrFVotR5.uX5vcM4Xkx21F0XrZSSjYDPnVd5isTvm', '管理员', '/api/files/access?path=avatars/avatar_f1d4a2772cbb436f9619283278c5fb71.jpg', 'ADMIN', 1, '2025-07-30 14:51:30', '2025-07-31 09:57:22');
INSERT INTO `tb_user` VALUES (2, 'admin1', '<EMAIL>', '$2a$10$2qDXDFecwj21CCN2Dfsz5ejDluU1tcEXiQb/WDlhoNhBY1aDwH2G2', '曹坤', '/api/files/access?path=avatars/avatar_f1d4a2772cbb436f9619283278c5fb71.jpg', 'USER', 1, '2025-07-30 14:58:29', '2025-07-30 14:58:29');
INSERT INTO `tb_user` VALUES (3, 'zhangsan', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '张三', '/api/files/access?path=avatars/avatar_f1d4a2772cbb436f9619283278c5fb71.jpg', 'USER', 1, '2025-07-30 17:05:05', '2025-07-31 09:57:23');
INSERT INTO `tb_user` VALUES (4, 'lisi', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '李四', '/api/files/access?path=avatars/avatar_f1d4a2772cbb436f9619283278c5fb71.jpg', 'USER', 1, '2025-07-30 17:05:05', '2025-07-31 09:57:25');
INSERT INTO `tb_user` VALUES (5, 'wangwu', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '王五', '/api/files/access?path=avatars/avatar_f1d4a2772cbb436f9619283278c5fb71.jpg', 'USER', 1, '2025-07-30 17:05:05', '2025-07-31 09:57:26');
INSERT INTO `tb_user` VALUES (6, 'zhaoliu', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '赵六', '/api/files/access?path=avatars/avatar_f1d4a2772cbb436f9619283278c5fb71.jpg', 'USER', 1, '2025-07-30 17:05:05', '2025-07-31 09:57:27');
INSERT INTO `tb_user` VALUES (7, 'sunqi', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '孙七', '/api/files/access?path=avatars/avatar_f1d4a2772cbb436f9619283278c5fb71.jpg', 'USER', 1, '2025-07-30 17:05:05', '2025-07-31 09:57:29');
INSERT INTO `tb_user` VALUES (8, 'zhouba', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '周八', '/api/files/access?path=avatars/avatar_f1d4a2772cbb436f9619283278c5fb71.jpg', 'USER', 1, '2025-07-30 17:05:05', '2025-07-31 09:57:30');
INSERT INTO `tb_user` VALUES (9, 'wujiu', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '吴九', '/api/files/access?path=avatars/avatar_f1d4a2772cbb436f9619283278c5fb71.jpg', 'USER', 1, '2025-07-30 17:05:05', '2025-07-31 09:57:31');
INSERT INTO `tb_user` VALUES (10, 'zhengshi', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '郑十', '/api/files/access?path=avatars/avatar_f1d4a2772cbb436f9619283278c5fb71.jpg', 'USER', 1, '2025-07-30 17:05:05', '2025-07-31 09:57:32');
INSERT INTO `tb_user` VALUES (11, 'editor1', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '编辑员1', '/api/files/access?path=avatars/avatar_f1d4a2772cbb436f9619283278c5fb71.jpg', 'EDITOR', 1, '2025-07-30 17:05:05', '2025-07-31 09:57:34');
INSERT INTO `tb_user` VALUES (12, 'editor2', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '编辑员2', '/api/files/access?path=avatars/avatar_f1d4a2772cbb436f9619283278c5fb71.jpg', 'EDITOR', 1, '2025-07-30 17:05:05', '2025-07-31 09:57:35');
INSERT INTO `tb_user` VALUES (13, 'testuser1', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '测试用户1', '/api/files/access?path=avatars/avatar_f1d4a2772cbb436f9619283278c5fb71.jpg', 'USER', 1, '2025-07-30 17:05:05', '2025-07-31 09:57:36');
INSERT INTO `tb_user` VALUES (14, 'testuser2', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '测试用户2', '/api/files/access?path=avatars/avatar_f1d4a2772cbb436f9619283278c5fb71.jpg', 'USER', 1, '2025-07-30 17:05:05', '2025-07-31 09:57:39');
INSERT INTO `tb_user` VALUES (15, 'caokun', '<EMAIL>', '$2a$10$cVT2in6DXIr5KFAa.yZY4exIv12yCU0z7Zru3HYzx7TnMn0gFD3Oe', 'caokun', '/api/files/access?path=avatars/avatar_e77ca76266ee4dee92f6129ce1a81d3e.jpg', 'USER', 1, '2025-07-31 10:22:09', '2025-07-31 10:22:09');

-- ----------------------------
-- Table structure for tb_user_favorite
-- ----------------------------
DROP TABLE IF EXISTS `tb_user_favorite`;
CREATE TABLE `tb_user_favorite`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `article_id` bigint NOT NULL COMMENT '文章ID',
  `status` tinyint NULL DEFAULT 1 COMMENT '收藏状态：1-已收藏，0-取消收藏',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_article`(`user_id` ASC, `article_id` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_article_id`(`article_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户收藏记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of tb_user_favorite
-- ----------------------------
INSERT INTO `tb_user_favorite` VALUES (1, 2, 1, 0, '2025-07-30 17:16:58', '2025-07-31 12:00:38');

-- ----------------------------
-- Table structure for tb_user_like
-- ----------------------------
DROP TABLE IF EXISTS `tb_user_like`;
CREATE TABLE `tb_user_like`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `article_id` bigint NOT NULL COMMENT '文章ID',
  `status` tinyint NULL DEFAULT 1 COMMENT '点赞状态：1-已点赞，0-取消点赞',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_article`(`user_id` ASC, `article_id` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_article_id`(`article_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户点赞记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of tb_user_like
-- ----------------------------
INSERT INTO `tb_user_like` VALUES (1, 2, 1, 0, '2025-07-30 17:10:33', '2025-07-30 17:10:33');
INSERT INTO `tb_user_like` VALUES (2, 15, 1, 1, '2025-07-31 12:04:25', '2025-07-31 12:04:25');

-- ----------------------------
-- Table structure for tb_user_role
-- ----------------------------
DROP TABLE IF EXISTS `tb_user_role`;
CREATE TABLE `tb_user_role`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_role_id`(`role_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户角色关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of tb_user_role
-- ----------------------------
INSERT INTO `tb_user_role` VALUES (1, 1, 1, '2025-07-30 14:51:30');

SET FOREIGN_KEY_CHECKS = 1;
