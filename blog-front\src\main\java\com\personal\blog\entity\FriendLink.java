package com.personal.blog.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 友情链接实体类
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("tb_friend_link")
public class FriendLink {

    /**
     * 链接ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 链接名称
     */
    @TableField("name")
    private String name;

    /**
     * 链接描述
     */
    @TableField("description")
    private String description;

    /**
     * 链接地址
     */
    @TableField("url")
    private String url;

    /**
     * 头像地址
     */
    @TableField("avatar")
    private String avatar;

    /**
     * 排序
     */
    @TableField("sort_order")
    private Integer sortOrder;

    /**
     * 是否启用
     */
    @TableField("is_enabled")
    private Integer isEnabled;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
} 