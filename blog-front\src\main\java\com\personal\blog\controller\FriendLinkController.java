package com.personal.blog.controller;

import com.personal.blog.entity.FriendLink;
import com.personal.blog.service.FriendLinkService;
import com.personal.blog.vo.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 友情链接控制器
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
@Tag(name = "友情链接管理", description = "友情链接相关接口")
@RestController
@RequestMapping("/api/friend-links")
@RequiredArgsConstructor
public class FriendLinkController {

    private final FriendLinkService friendLinkService;

    @Operation(summary = "获取所有启用的友情链接")
    @GetMapping
    public Result<List<FriendLink>> getEnabledFriendLinks() {
        List<FriendLink> friendLinks = friendLinkService.getEnabledFriendLinks();
        return Result.success(friendLinks);
    }
} 