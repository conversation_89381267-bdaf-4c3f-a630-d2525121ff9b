<template>
  <div class="home-content">
    <div class="section-header">
      <div class="header-content">
        <h2 class="section-title">
          <span class="title-icon">
            <el-icon><Document /></el-icon>
          </span>
          最新文章
        </h2>
        <p class="section-subtitle">发现最新的技术文章和见解</p>
      </div>
      <div class="section-actions">
        <el-button-group class="sort-buttons">
          <el-button
            :type="sortBy === 'latest' ? 'primary' : ''"
            :class="{ active: sortBy === 'latest' }"
            @click="changeSortBy('latest')"
          >
            <el-icon><Clock /></el-icon>
            最新
          </el-button>
          <el-button
            :type="sortBy === 'hot' ? 'primary' : ''"
            :class="{ active: sortBy === 'hot' }"
            @click="changeSortBy('hot')"
          >
            <el-icon><Fire /></el-icon>
            热门
          </el-button>
        </el-button-group>
      </div>
    </div>

    <!-- 文章网格 -->
    <div class="articles-grid" v-loading="articleStore.loading">
      <ArticleCard
        v-for="article in articleStore.articles"
        :key="article.id"
        :article="article"
        @click="$router.push(`/article/${article.id}`)"
      />
    </div>

    <!-- 空状态 -->
    <div v-if="!articleStore.articles.length && !articleStore.loading" class="empty-state">
      <div class="empty-content">
        <el-icon class="empty-icon"><Document /></el-icon>
        <h3>暂无文章</h3>
        <p>还没有发布任何文章，敬请期待</p>
      </div>
    </div>

    <!-- 加载更多 -->
    <div class="load-more" v-if="articleStore.hasMore">
      <el-button
        :loading="articleStore.loading"
        @click="loadMore"
        size="large"
        class="load-more-btn"
      >
        <el-icon><Refresh /></el-icon>
        加载更多
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useArticleStore } from '@/store/article'
import ArticleCard from '@/components/article/ArticleCard.vue'

const articleStore = useArticleStore()
const sortBy = ref('latest')

// 切换排序方式
const changeSortBy = async (type) => {
  if (sortBy.value === type) return

  sortBy.value = type
  articleStore.clearArticles()

  if (type === 'hot') {
    // 使用热度计算获取热门文章
    await articleStore.fetchHotArticles()
  } else {
    // 使用创建时间排序获取最新文章
    const params = { sortBy: 'create_time', sortOrder: 'desc' }
    await articleStore.fetchArticles(params)
  }
}

// 加载更多文章
const loadMore = async () => {
  if (sortBy.value === 'hot') {
    // 加载更多热门文章
    await articleStore.loadMoreHotArticles()
  } else {
    // 加载更多最新文章
    const params = { sortBy: 'create_time', sortOrder: 'desc' }
    await articleStore.loadMoreArticles(params)
  }
}

// 初始化数据
onMounted(async () => {
  // 如果已有数据则不重复加载
  if (articleStore.articles.length === 0) {
    await articleStore.fetchArticles({ sortBy: 'create_time', sortOrder: 'desc' })
  }
})
</script>

<style scoped>
.home-content {
  width: 100%;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-lg);
  gap: var(--spacing-lg);
}

.header-content {
  flex: 1;
}

.section-title {
  font-size: var(--font-2xl);
  font-weight: 700;
  color: var(--primary-color);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
}

.title-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(45deg, var(--primary-color), var(--primary-light));
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.section-subtitle {
  font-size: var(--font-md);
  color: var(--text-secondary);
  line-height: 1.6;
}

.section-actions {
  display: flex;
  align-items: center;
}

.sort-buttons {
  background: white;
  border-radius: var(--radius-lg);
  padding: var(--spacing-xs);
  box-shadow: var(--shadow-sm);
}

.sort-buttons .el-button {
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-weight: 500;
}

.sort-buttons .el-button.active {
  background: var(--primary-color);
  color: white;
}

.articles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  text-align: center;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-lg);
}

.empty-icon {
  font-size: 48px;
  color: var(--text-light);
}

.empty-content h3 {
  font-size: var(--font-lg);
  color: var(--text-secondary);
  margin: 0;
}

.empty-content p {
  color: var(--text-light);
  margin: 0;
}

.load-more {
  display: flex;
  justify-content: center;
  margin-top: var(--spacing-lg);
}

.load-more-btn {
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--radius-lg);
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  background: linear-gradient(45deg, var(--primary-color), var(--primary-light));
  border: none;
  color: white;
  transition: all 0.3s ease;
}

.load-more-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* 动画 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .articles-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-lg);
  }
}

@media (max-width: 768px) {
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);
  }

  .section-title {
    font-size: var(--font-xl);
  }

  .title-icon {
    width: 36px;
    height: 36px;
    font-size: 18px;
  }

  .articles-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .section-title {
    font-size: var(--font-lg);
  }

  .title-icon {
    width: 32px;
    height: 32px;
    font-size: 16px;
  }
}
</style>
